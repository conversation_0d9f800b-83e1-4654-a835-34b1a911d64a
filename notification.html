<!DOCTYPE html>
<html lang="en" style="margin: 0; padding: 0; width: 100%; height: 100vh;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quadrant ID Notification</title>
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
</head>
<body style="margin: 0; padding: 0; width: 100%; height: 100vh; font-family: Inter, sans-serif; font-feature-settings: 'liga' 1, 'calt' 1; background-color: #0f172b; color: white;">
    <div class="container" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; min-width: 100%; gap: 24px;">
        <img src="%NOTIFICATION_ICON%" alt="Notification image" style="width: 128px; height: 128px; object-fit: contain; object-position: center; border-radius: 50%;"></img>
        <div class="messageContainer" style="display: flex; flex-direction: column; align-items: center; justify-content: center; background: #314158; border-radius: 16px; padding: 12px; margin-top: 24px; font-weight: 600;">
             <h1 class="title" style="font-size: 24px; font-weight: 900;">%NOTIFICATION_TITLE%</h1>
        </div>
        <div class="messageContainer" style="display: flex; flex-direction: column; align-items: center; justify-content: center; background: #314158; border-radius: 16px; padding: 12px; margin-top: 24px; font-weight: 600;">
            %NOTIFICATION_MESSAGE%
        </div>
    </div>
</body>
</html>

