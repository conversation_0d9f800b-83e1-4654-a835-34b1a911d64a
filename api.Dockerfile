FROM rust:alpine AS builder

COPY . /src

ENV RUSTFLAGS='-C target-feature=-crt-static'
RUN apk update && apk upgrade --available
RUN apk add --no-cache libpq-dev build-base openssl-dev zlib-dev
RUN cd src && cargo build -r
RUN mkdir -p /app
RUN install -Dm755 src/target/release/qntapi /app/api
RUN rm -rf /src

FROM alpine:latest
RUN apk update && apk upgrade --available
RUN apk add --no-cache postgresql-client openssl libgcc
COPY --from=builder /app/api /app/api
CMD ["/app/api"]

EXPOSE 6001