[package]
edition = "2024"
version = "6.5.8"
name = "qntapi"
publish = false

[dependencies]
actix-cors = "0.7.1"
actix-web = { version = "4.11.0", features = ["openssl"] }
anyhow = "1.0.99"
chrono = { version = "0.4.41", features = ["serde"] }
env_logger = { version = "0.11.8", features = [
    "humantime",
    "auto-color",
    "color",
] }
log = "0.4.27"
qstring = "0.7.2"
reqwest = { version = "0.12.23", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.143"
tokio = { version = "1.47.1", features = ["full"] }
uuid = { version = "1.18.0", features = ["v5", "sha1", "v7"] }
dotenvy = "0.15.7"
rand = "0.9.2"
rss = { version = "2.0.12", features = ["builders", "chrono", "serde", "url"] }
lettre = "0.11.18"
argon2 = { version = "0.5.3", features = ["simple"] }
jsonwebtoken = "9.3.1"
totp-rs = { version = "5.7.0", features = [
    "gen_secret",
    "otpauth",
    "serde",
    "qr",
    "zeroize",
] }
sha2 = "0.10.8"
hmac = "0.12.1"
hex = "0.4.3"
constant_time_eq = "0.4.2"
lemonsqueezy = "0.1.3"
tokio-retry = "0.3.0"
sea-orm = { version = "1.1.14", features = [
    "sqlx-postgres",
    "runtime-tokio-native-tls",
    "macros",
    "with-chrono",
    "postgres-array",
    "with-uuid",
    "with-json",
] }

[profile.release]
codegen-units = 1
debug = 0
lto = "thin"
opt-level = 3
strip = "symbols"
