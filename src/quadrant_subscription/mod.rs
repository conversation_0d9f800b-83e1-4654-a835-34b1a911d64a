use crate::util::entities::{
    account_user_groups, prelude::*, quadrant_accounts, user_subscription,
};
use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use hmac::{Hmac, Mac};
use lemonsqueezy::subscriptions::Subscriptions;
use qntapi::WebData;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, Set,
    TransactionTrait,
};
use serde::{Deserialize, Serialize};
use sha2::Sha256;
use tokio_retry::{
    Retry,
    strategy::{ExponentialBackoff, jitter},
};

// Retry configuration
const MAX_RETRIES: u32 = 10;
const INITIAL_RETRY_INTERVAL_MS: u64 = 500;

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct LemonSqueezyMeta {
    pub event_name: String,
    pub custom_data: Option<CustomInfo>,
}

#[derive(<PERSON><PERSON>, Debug, Deserialize, Serialize)]
pub struct LemonSqueezyHook {
    pub meta: LemonSqueezyMeta,
    pub data: SubscriptionResponse,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SubscriptionResponse {
    #[serde(rename = "type")]
    pub type_field: String,
    pub id: String,
    pub attributes: SubscriptionAttributes,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SubscriptionAttributes {
    pub store_id: i64,
    pub customer_id: i64,
    pub order_id: i64,
    pub order_item_id: i64,
    pub product_id: i64,
    pub variant_id: i64,
    pub product_name: String,
    pub variant_name: String,
    pub user_name: String,
    pub user_email: String,
    pub status: String,
    pub status_formatted: String,
    pub test_mode: bool,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CustomInfo {
    pub uid: String,
}

#[post("/api/v3/quadrant/subscription")]
pub async fn handle_subscriptions(
    _req: HttpRequest,
    bytes: web::Bytes,
    webdata: web::Data<WebData>,
) -> impl Responder {
    let headers = _req.headers();
    let signature = headers.get("X-Signature");
    if signature.is_none() {
        return HttpResponse::Forbidden().body("No signature provided");
    }
    let signature_res = signature.unwrap().to_str();
    if signature_res.is_err() {
        return HttpResponse::Forbidden().body("Invalid signature format");
    }
    let signature = signature_res.unwrap().to_string();

    // Generate a sha256 of the provided signature

    let mut mac = Hmac::<Sha256>::new_from_slice(webdata.lemon.webhook_secret.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(&bytes);
    let hash_sum = hex::encode(mac.finalize().into_bytes());

    if !constant_time_eq::constant_time_eq(hash_sum.as_bytes(), signature.as_bytes()) {
        log::warn!("Invalid signature, while trying to submit subscription");
        #[cfg(not(debug_assertions))]
        {
            return HttpResponse::Forbidden().body(format!("Invalid signature"));
        }
    }

    let body = serde_json::from_slice::<LemonSqueezyHook>(&bytes);

    if body.is_err() {
        return HttpResponse::BadRequest()
            .body(format!("Failed to parse request: {}", body.err().unwrap()));
    }
    let body = body.unwrap();

    if !body.meta.event_name.starts_with("subscription") {
        return HttpResponse::NotImplemented()
            .body("This webhook only handles subscription events");
    }
    #[cfg(not(debug_assertions))]
    {
        if body.data.attributes.test_mode {
            return HttpResponse::Ok().body("Test mode, no actual changes applied");
        }
    }
    let subscription_status = body.data.attributes.status.clone();
    let product_id = body.data.attributes.product_id;
    let subscription_id = body.data.id;
    let store_id = body.data.attributes.store_id.clone();
    let email = body.data.attributes.user_email.clone();
    let order_id = body.data.attributes.order_id;

    if store_id != webdata.lemon.store_id {
        return HttpResponse::BadRequest().body("This webhook only handles Quadrant store events");
    }
    if product_id != webdata.lemon.plus_sub_id {
        return HttpResponse::BadRequest()
            .body("This webhook only handles Quadrant subscription events");
    }
    let group_id = webdata.lemon.plus_sub_gid.clone();
    let sub_id = subscription_id.clone();

    let res = if subscription_status == "active"
        || subscription_status == "on_trial"
        || subscription_status == "canceled"
    {
        add_to_subscription(
            &webdata.conn,
            group_id,
            email,
            subscription_id,
            body.meta.custom_data,
        )
        .await
    } else {
        delete_from_subscription(
            &webdata.conn,
            group_id,
            email,
            subscription_id,
            body.meta.custom_data,
        )
        .await
    };

    if let Err(e) = res {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to update the subscription: {}", e));
    }

    match res.unwrap() {
        Some(_) => HttpResponse::Ok().body("Ok"),
        None => {
            // We only get none if the subscription is duplicated
            // therefore we have to cancel it IF IT IS A NEW SUBSCRIPTION
            if body.meta.event_name == "subscription_created" {
                match refund_subscription(sub_id, order_id, webdata).await {
                    Ok(_) => HttpResponse::Ok().body("Refunded subscription"),
                    Err(e) => HttpResponse::InternalServerError()
                        .body(format!("Failed to refund the subscription: {}", e)),
                }
            } else {
                HttpResponse::Ok().body("Ok")
            }
        }
    }
}

/// Adds a user to a subscription and associated group
async fn add_to_subscription(
    db: &DatabaseConnection,
    group: String,
    user: String,
    subscription_id: String,
    custom_data: Option<CustomInfo>,
) -> Result<Option<()>, anyhow::Error> {
    let txn = db.begin().await?;

    // Get account ID either from custom data or by email lookup
    let acc_id = if let Some(data) = custom_data {
        data.uid
    } else {
        let account = QuadrantAccounts::find()
            .filter(quadrant_accounts::Column::Email.eq(user))
            .one(&txn)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Account not found"))?;
        account.id
    };

    // Check if user group already exists
    let group_exists = AccountUserGroups::find()
        .filter(account_user_groups::Column::AccountId.eq(&acc_id))
        .filter(account_user_groups::Column::GroupId.eq(&group))
        .one(&txn)
        .await?;

    // Add user group if it doesn't exist
    if group_exists.is_none() {
        let group_model = account_user_groups::ActiveModel {
            account_id: Set(acc_id.clone()),
            group_id: Set(group),
            ..Default::default()
        };
        group_model.insert(&txn).await?;
    }

    // Check if subscription already exists
    let sub_exists = UserSubscription::find()
        .filter(user_subscription::Column::AccountId.eq(&acc_id))
        .filter(user_subscription::Column::SubId.eq(&subscription_id))
        .one(&txn)
        .await?;

    let result = if sub_exists.is_none() {
        // Add subscription if it doesn't exist
        let sub_model = user_subscription::ActiveModel {
            account_id: Set(acc_id),
            sub_id: Set(subscription_id),
            ..Default::default()
        };
        sub_model.insert(&txn).await?;
        Some(())
    } else {
        None
    };

    txn.commit().await?;
    Ok(result)
}

/// Removes a user from a subscription and associated group
async fn delete_from_subscription(
    db: &DatabaseConnection,
    group: String,
    user: String,
    subscription_id: String,
    custom_data: Option<CustomInfo>,
) -> Result<Option<()>, anyhow::Error> {
    let txn = db.begin().await?;

    // Get account ID either from custom data or by email lookup
    let acc_id = if let Some(data) = custom_data {
        data.uid
    } else {
        let account = QuadrantAccounts::find()
            .filter(quadrant_accounts::Column::Email.eq(user))
            .one(&txn)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Account not found"))?;
        account.id
    };

    // Remove user group
    AccountUserGroups::delete_many()
        .filter(account_user_groups::Column::AccountId.eq(&acc_id))
        .filter(account_user_groups::Column::GroupId.eq(&group))
        .exec(&txn)
        .await?;

    // Remove subscription
    UserSubscription::delete_many()
        .filter(user_subscription::Column::AccountId.eq(&acc_id))
        .filter(user_subscription::Column::SubId.eq(&subscription_id))
        .exec(&txn)
        .await?;

    txn.commit().await?;
    Ok(Some(()))
}

async fn refund_subscription(
    subscription_id: String,
    order_id: i64,
    webdata: web::Data<WebData>,
) -> Result<(), anyhow::Error> {
    let retry_strategy = ExponentialBackoff::from_millis(INITIAL_RETRY_INTERVAL_MS)
        .map(jitter) // Add randomness to prevent thundering herd
        .take(MAX_RETRIES as usize);

    let result = Retry::spawn(retry_strategy, || async {
        let ls = webdata.lemon.ls_instance.clone();
        let sub = Subscriptions::build(ls.clone());
        let _subs = sub.cancel(subscription_id.parse()?).await?;

        let reqwest_client = reqwest::Client::new();
        let res = reqwest_client
            .post(format!(
                "https://api.lemonsqueezy.com/v1/orders/{}/refund",
                order_id
            ))
            .bearer_auth(webdata.lemon.api_key.clone())
            .send()
            .await?;

        if !res.status().is_success() {
            let error_msg = format!(
                "Failed to refund the subscription ({}/{})",
                res.status(),
                res.text().await?
            );
            return Err(anyhow::anyhow!(error_msg));
        }
        Ok(())
    });
    result.await
}
