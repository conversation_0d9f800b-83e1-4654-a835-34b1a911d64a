use actix_web::{HttpRequest, HttpResponse, Responder, get, web};
use qstring::QString;
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};

use crate::quadrant_usage::AppInfoAnonymous;
use crate::util::entities::quadrant_usage;
use qntapi::WebData;

/// Handles the backend logic for retrieving usage information
async fn get_usage_info_backend(_req: HttpRequest, data: web::Data<WebData>) -> HttpResponse {
    // Parse the query string which contains a hardware_id
    let qs = QString::from(_req.query_string());
    let hwid: Option<&str> = qs.get("hardware_id");

    // If a specific hardware id is specified the API returns the whole usage info,
    // since the only way that this hardware id is known is through the Minecraft Modpack Manager
    if let Some(hwid) = hwid {
        // Query for specific hardware ID
        let find_res = quadrant_usage::Entity::find()
            .filter(quadrant_usage::Column::HardwareId.eq(hwid))
            .one(&data.conn)
            .await;

        match find_res {
            Ok(Some(usage_info)) => match serde_json::to_string_pretty(&usage_info) {
                Ok(json) => HttpResponse::Ok().body(json),
                Err(_) => {
                    HttpResponse::InternalServerError().body("Failed to serialize usage info")
                }
            },
            Ok(None) => HttpResponse::InternalServerError().body("No usage info found."),
            Err(_) => HttpResponse::InternalServerError().body("Failed to get your usage info"),
        }
    } else {
        // Query for all usage info
        let find_res = quadrant_usage::Entity::find().all(&data.conn).await;

        match find_res {
            Ok(results) => {
                if results.is_empty() {
                    return HttpResponse::InternalServerError().body("No usage info found.");
                }

                // Convert to anonymous format
                let usages: Vec<AppInfoAnonymous> =
                    results.into_iter().map(|result| result.into()).collect();

                match serde_json::to_string_pretty(&usages) {
                    Ok(json) => HttpResponse::Ok().body(json),
                    Err(_) => {
                        HttpResponse::InternalServerError().body("Failed to serialize usage info")
                    }
                }
            }
            Err(_) => HttpResponse::InternalServerError().body("Failed to get your usage info"),
        }
    }
}

/// API endpoint for retrieving usage information (v3)
#[get("/api/v3/quadrant/usage/get")]
async fn get_usage_info_v3(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    get_usage_info_backend(_req, data).await
}
