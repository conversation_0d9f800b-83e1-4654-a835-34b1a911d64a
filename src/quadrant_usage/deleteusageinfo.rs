use actix_web::{HttpRequest, HttpResponse, Responder, delete, web};
use qstring::QString;
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};

use crate::util::entities::{quadrant_share, quadrant_usage};
use qntapi::{ApiKeySearchFilter, WebData, get_api_keys};

/// Handles the backend logic for deleting usage information
async fn delete_usage_info_backend(_req: HttpRequest, data: web::Data<WebData>) -> HttpResponse {
    // Verify API key
    let api_key_filter = ApiKeySearchFilter {
        product: "quadrant".to_string(),
        scope: "usage_info".to_string(),
    };
    let get_keys_res = get_api_keys(data.as_ref().to_owned(), api_key_filter).await;
    if get_keys_res.is_err() {
        return HttpResponse::InternalServerError()
            .body("Failed to verify your API key (doesn't mean that it's invalid)");
    }
    let authentication_keys: Vec<String> = get_keys_res.unwrap();

    // Validate authorization header
    if _req.headers().get("Authorization").is_none()
        || !authentication_keys.contains(
            &_req
                .headers()
                .get("Authorization")
                .unwrap()
                .to_str()
                .unwrap()
                .to_owned(),
        )
    {
        return HttpResponse::Forbidden().body("Your API key is invalid.");
    }

    // Validate User-Agent
    if _req.headers().get("User-Agent").is_none()
        || !(_req
            .headers()
            .get("User-Agent")
            .unwrap()
            .to_str()
            .unwrap()
            .contains("mrquantumoff.dev"))
    {
        return HttpResponse::Forbidden().body("Request was not sent from a mrquantumoff.dev app");
    }

    // Get hardware ID from query string
    let qs = QString::from(_req.query_string());
    let hwid: Option<&str> = qs.get("hardware_id");

    if hwid.is_none() {
        return HttpResponse::NotFound().body("Hardware ID was not specified");
    }
    let hwid = hwid.unwrap().to_string();

    // Find usage info for the hardware ID
    let usage_info = quadrant_usage::Entity::find()
        .filter(quadrant_usage::Column::HardwareId.eq(hwid.clone()))
        .one(&data.conn)
        .await;

    match usage_info {
        Ok(Some(info)) => {
            // Delete usage info
            if let Err(e) = quadrant_usage::Entity::delete_by_id(info.entry_timestamp)
                .exec(&data.conn)
                .await
            {
                return HttpResponse::InternalServerError()
                    .body(format!("Failed to delete usage info: {}", e));
            }

            // Delete associated sharing codes
            if let Err(_) = quadrant_share::Entity::delete_many()
                .filter(quadrant_share::Column::HardwareId.eq(info.hardware_id))
                .exec(&data.conn)
                .await
            {
                return HttpResponse::Ok()
                    .body("Failed to delete the sharing codes, but the usage info was deleted.");
            }

            HttpResponse::Ok().body("DELETED")
        }
        Ok(None) => HttpResponse::Ok().body("No usage info found to delete"),
        Err(e) => HttpResponse::InternalServerError().body(format!("Database error: {}", e)),
    }
}

/// API endpoint for deleting usage information (v3)
#[delete("/api/v3/quadrant/usage/delete")]
async fn delete_usage_info_v3(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    delete_usage_info_backend(_req, data).await
}
