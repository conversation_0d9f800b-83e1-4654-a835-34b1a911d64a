use serde::{Deserialize, Serialize};

use crate::util::entities::quadrant_usage;

pub mod deleteusageinfo;
pub mod getusageinfo;
pub mod submitusageinfo;
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct AppInfoAnonymous {
    version: String,
    os: String,
    modrinth_usage: i64,
    curseforge_usage: i64,
    reference_file_usage: i64,
    manual_input_usage: i64,
    country: String,
}

impl From<quadrant_usage::Model> for AppInfoAnonymous {
    fn from(value: quadrant_usage::Model) -> Self {
        AppInfoAnonymous {
            version: value.version,
            os: value.os,
            modrinth_usage: value.modrinth_usage,
            curseforge_usage: value.curseforge_usage,
            reference_file_usage: value.reference_file_usage,
            manual_input_usage: value.manual_input_usage,
            country: value.country,
        }
    }
}
