use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use chrono::prelude::*;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use serde::{Deserialize, Serialize};

use crate::util::entities::quadrant_usage::{self, ActiveModel as QuadrantUsageModel};
use qntapi::{ApiKeySearchFilter, WebData, get_api_keys};

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
struct SerAppInfo {
    pub version: String,
    pub os: String,
    pub modrinth_usage: i64,
    pub curseforge_usage: i64,
    pub reference_file_usage: i64,
    pub manual_input_usage: i64,
    pub hardware_id: String,
    pub date: String,
    pub country: String,
}

impl From<SerAppInfo> for QuadrantUsageModel {
    fn from(value: SerAppInfo) -> Self {
        let timestamp = Utc::now();
        QuadrantUsageModel {
            version: Set(value.version),
            os: Set(value.os),
            modrinth_usage: Set(value.modrinth_usage),
            curseforge_usage: Set(value.curseforge_usage),
            reference_file_usage: Set(value.reference_file_usage),
            manual_input_usage: Set(value.manual_input_usage),
            hardware_id: Set(value.hardware_id),
            country: Set(value.country),
            entry_timestamp: Set(timestamp.into()),
        }
    }
}

/// Handles the backend logic for submitting usage information
async fn submit_usage_info_backend(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
) -> HttpResponse {
    // Verify API key
    let api_key_filter = ApiKeySearchFilter {
        product: "quadrant".to_string(),
        scope: "usage_info".to_string(),
    };
    let get_keys_res = get_api_keys(webdata.as_ref().to_owned(), api_key_filter).await;
    if get_keys_res.is_err() {
        return HttpResponse::InternalServerError()
            .body("Failed to verify your API key (doesn't mean that it's invalid)");
    }
    let authentication_keys: Vec<String> = get_keys_res.unwrap();

    // Validate authorization header
    if _req.headers().get("Authorization").is_none()
        || !authentication_keys.contains(
            &_req
                .headers()
                .get("Authorization")
                .unwrap()
                .to_str()
                .unwrap()
                .to_owned(),
        )
    {
        return HttpResponse::Forbidden().body("Your API key is invalid.");
    }

    // Validate User-Agent
    if _req.headers().get("User-Agent").is_none()
        || !(_req
            .headers()
            .get("User-Agent")
            .unwrap()
            .to_str()
            .unwrap()
            .contains("mrquantumoff.dev"))
    {
        return HttpResponse::Forbidden().body("Request was not sent from a mrquantumoff.dev app");
    }

    // Parse request body
    let new_data_res = serde_json::from_str::<SerAppInfo>(&req_body);
    let data: QuadrantUsageModel = match new_data_res {
        Ok(res_data) => res_data.into(),
        Err(_) => return HttpResponse::BadRequest().body("Failed to parse JSON"),
    };

    // Validate timestamp
    let utc = Utc::now();
    let req_utc: DateTime<Utc> = data.entry_timestamp.as_ref().clone().into();

    // This could help prevent fake entries in the database
    if !(utc.day() == req_utc.day()
        && utc.hour() == req_utc.hour()
        && utc.month() == req_utc.month()
        && req_utc.year() == utc.year())
    {
        return HttpResponse::BadRequest().body("Suspicious Request");
    }

    // Check if entry exists and update/create accordingly
    let existing_entry = quadrant_usage::Entity::find()
        .filter(quadrant_usage::Column::HardwareId.eq(data.hardware_id.as_ref()))
        .one(&webdata.conn)
        .await;

    match existing_entry {
        Ok(Some(entry)) => {
            // Delete existing entry
            if let Err(_) = quadrant_usage::Entity::delete_by_id(entry.entry_timestamp)
                .exec(&webdata.conn)
                .await
            {
                return HttpResponse::InternalServerError().body("Failed to update entry");
            }
        }
        Ok(None) => (),
        Err(_) => return HttpResponse::InternalServerError().body("Database error"),
    }

    // Insert new entry
    match data.insert(&webdata.conn).await {
        Ok(_) => HttpResponse::Accepted().body("Updated/Created successfully"),
        Err(_) => HttpResponse::InternalServerError().body("Failed to update or create an entry"),
    }
}

/// API endpoint for submitting usage information (v3)
#[post("/api/v3/quadrant/usage/submit")]
async fn submit_usage_info_v3(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
) -> impl Responder {
    submit_usage_info_backend(_req, req_body, webdata).await
}
