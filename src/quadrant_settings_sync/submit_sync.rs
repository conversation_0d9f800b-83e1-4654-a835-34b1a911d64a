use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use chrono::prelude::*;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};

use crate::quadrant_settings_sync::shared::SettingSyncExternal;
use crate::util::entities::{quadrant_accounts, quadrant_settings_sync};
use qntapi::{WebData, verify_jwt};

/// Submit settings sync data for a user
///
/// # Endpoint
/// POST /api/v3/quadrant/settings_sync/submit
#[post("/api/v3/quadrant/settings_sync/submit")]
pub async fn quadrant_settings_sync_submit_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    // Verify JWT token
    let claims = verify_jwt(_req);
    if claims.is_err() {
        return HttpResponse::Unauthorized().body(claims.err().unwrap().to_string());
    }
    let claims = claims.unwrap();

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Parse request body
    let quadrant_sync_raw = serde_json::from_str::<SettingSyncExternal>(&req_body);
    if quadrant_sync_raw.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse the body");
    }
    let q_sync_request = quadrant_sync_raw.unwrap();

    // Find account by session generation
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => {
            return HttpResponse::BadRequest()
                .body("Account existence check fail 1: Account does not exist!");
        }
        Err(e) => {
            return HttpResponse::BadRequest()
                .body(format!("Account existence check fail 1: {}", e));
        }
    };

    // Find current sync data
    let current_sync = quadrant_settings_sync::Entity::find()
        .filter(quadrant_settings_sync::Column::UserId.eq(&account.id))
        .one(&data.conn)
        .await;

    match current_sync {
        Ok(Some(existing_sync)) => {
            // Compare sync dates
            let new_sync_date = match DateTime::parse_from_rfc3339(&q_sync_request.sync_date) {
                Ok(date) => date,
                Err(_) => return HttpResponse::BadRequest().body("Invalid sync date format"),
            };
            let existing_sync_date = existing_sync.sync_date.to_utc();

            if existing_sync_date > new_sync_date {
                return HttpResponse::BadRequest().body("Existing sync is newer");
            }

            // Update existing sync
            let mut update_model = quadrant_settings_sync::ActiveModel::from(existing_sync);
            update_model.settings = Set(q_sync_request.settings);
            update_model.sync_date = Set(new_sync_date);

            match update_model.update(&data.conn).await {
                Ok(_) => HttpResponse::Ok().body("Updated/Created"),
                Err(e) => HttpResponse::InternalServerError()
                    .body(format!("Failed to update sync: {}", e)),
            }
        }
        Ok(None) => {
            // Create new sync
            let new_sync_date = match DateTime::parse_from_rfc3339(&q_sync_request.sync_date) {
                Ok(date) => date,
                Err(_) => return HttpResponse::BadRequest().body("Invalid sync date format"),
            };

            let new_sync = quadrant_settings_sync::ActiveModel {
                user_id: Set(account.id),
                settings: Set(q_sync_request.settings),
                sync_date: Set(new_sync_date),
            };

            match new_sync.insert(&data.conn).await {
                Ok(_) => HttpResponse::Ok().body("Updated/Created"),
                Err(e) => HttpResponse::InternalServerError()
                    .body(format!("Failed to create sync: {}", e)),
            }
        }
        Err(e) => HttpResponse::InternalServerError()
            .body(format!("Failed to check existing sync: {}", e)),
    }
}
