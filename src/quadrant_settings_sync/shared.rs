use crate::util::entities::quadrant_settings_sync;
use serde::{Deserialize, Serialize};
#[derive(<PERSON><PERSON>, Debug, Deserialize, Serialize)]
pub struct SettingSyncExternal {
    pub settings: String,
    pub sync_date: String,
}

impl From<quadrant_settings_sync::Model> for SettingSyncExternal {
    fn from(value: quadrant_settings_sync::Model) -> Self {
        SettingSyncExternal {
            settings: value.settings,
            sync_date: value.sync_date.to_utc().to_rfc3339(),
        }
    }
}
