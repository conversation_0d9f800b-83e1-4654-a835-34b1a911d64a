use crate::quadrant_settings_sync::shared::SettingSyncExternal;
use crate::util::entities::{quadrant_accounts, quadrant_settings_sync};
use actix_web::{HttpRequest, HttpResponse, Responder, get, web};
use qntapi::{WebData, verify_jwt};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};

/// Get settings sync data for a user
///
/// # Endpoint
/// GET /api/v3/quadrant/settings_sync/get
#[get("/api/v3/quadrant/settings_sync/get")]
pub async fn quadrant_settings_sync_get_v3(
    _req: HttpRequest,
    data: web::Data<WebData>,
) -> impl Responder {
    let claims = verify_jwt(_req);

    if claims.is_err() {
        return HttpResponse::Unauthorized().body(claims.err().unwrap().to_string());
    }
    let claims = claims.unwrap();

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Find account by session generation
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::NotFound().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Find settings sync data for the account
    match quadrant_settings_sync::Entity::find()
        .filter(quadrant_settings_sync::Column::UserId.eq(account.id))
        .one(&data.conn)
        .await
    {
        Ok(Some(sync_data)) => HttpResponse::Ok()
            .body(serde_json::to_string_pretty(&SettingSyncExternal::from(sync_data)).unwrap()),
        Ok(None) => HttpResponse::NotFound().body("No existing sync found"),
        Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
    }
}
