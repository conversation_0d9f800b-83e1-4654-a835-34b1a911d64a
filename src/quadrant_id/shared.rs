use actix_web::HttpResponse;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct IPAddressInfo {
    pub country: String,
    pub city: String,
    pub isp: String,
    #[serde(alias = "query")]
    pub ip: String,
    #[serde(alias = "countryCode")]
    pub country_code: String,
}

pub fn check_password(password: &str) -> Result<(), HttpResponse> {
    if password.len() < 12 || password.len() > 32 {
        return Err(
            HttpResponse::BadRequest().body("Password must be between 12 and 32 characters")
        );
    }
    let mut numbers = 0;
    let mut special_chars = 0;

    for pass_char in password.chars() {
        if !pass_char.is_ascii() || pass_char.is_ascii_whitespace() {
            return Err(HttpResponse::BadRequest()
                .body("Password must contain only ASCII characters without whitespacees"));
        }
        if pass_char.is_numeric() {
            numbers += 1;
        } else if pass_char.is_ascii_punctuation() {
            special_chars += 1;
        }
    }
    if numbers < 2 || special_chars < 2 {
        return Err(HttpResponse::BadRequest()
            .body("Password must contain at least two numbers and two special characters"));
    }

    Ok(())
}

pub fn check_login(login: &str) -> Result<(), HttpResponse> {
    if login.len() < 6 || login.len() > 32 {
        return Err(HttpResponse::BadRequest().body("Login must be between 6 and 32 characters"));
    }

    for login_char in login.chars() {
        if !login_char.is_ascii_alphanumeric() {
            return Err(HttpResponse::BadRequest().body(
                "Login must contain only ASCII characters without whitespaces or punctuation",
            ));
        }
    }
    Ok(())
}
