use actix_web::{HttpRequest, HttpResponse, Responder, get, post, web};
use argon2::{Argon2, PasswordHash, PasswordVerifier};
use chrono::{DateTime, Utc};
use sea_orm::*;
use serde::{Deserialize, Serialize};

use crate::util::entities::{quadrant_oauth2_apps, quadrant_oauth2_requests};
use qntapi::{Claims, WebData};

/// Request structure for OAuth2 app details
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct OAuth2Request {
    pub client_id: String,
}

/// Request structure for OAuth2 sign-in
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub struct OAuth2SignIn {
    pub client_id: String,
    pub client_secret: String,
    pub auth_code: String,
}

/// Full OAuth2 sign-in request structure
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct OAuth2SignInFull {
    pub client_id: String,
    pub client_secret: String,
    pub code: String,
    pub grant_type: String,
}

/// OAuth2 sign-in response structure
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct OAuth2SignInFullResponse {
    pub token_type: String,
    pub scope: String,
    pub access_token: String,
    pub expires_in: i64,
    pub refresh_token: String,
}

/// OAuth2 app details response structure
#[derive(Debug, Clone, PartialEq, Eq, Serialize)]
pub struct QuadrantOAuth2Details {
    pub display_name: String,
    pub description: String,
    pub image: String,
}

impl From<quadrant_oauth2_apps::Model> for QuadrantOAuth2Details {
    fn from(value: quadrant_oauth2_apps::Model) -> Self {
        QuadrantOAuth2Details {
            display_name: value.display_name,
            description: value.description,
            image: value.image,
        }
    }
}

/// Handles OAuth2 app details request
async fn oauth2_request_backend(
    _req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Query<OAuth2Request>,
) -> HttpResponse {
    // Find OAuth2 app by client_id
    let app = match quadrant_oauth2_apps::Entity::find()
        .filter(quadrant_oauth2_apps::Column::ClientId.eq(info.client_id.clone()))
        .one(&data.conn)
        .await
    {
        Ok(Some(app)) => app,
        Ok(None) => return HttpResponse::BadRequest().body("App does not exist!"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("App existence check failed: {}", e));
        }
    };

    // Convert to response format and serialize
    let app_details = QuadrantOAuth2Details::from(app);
    match serde_json::to_string_pretty(&app_details) {
        Ok(json) => HttpResponse::Ok().body(json),
        Err(_) => HttpResponse::InternalServerError().body("Failed to parse JSON"),
    }
}

#[get("/api/v3/account/oauth2/app_details/get")]
pub async fn oauth2_request_v3(
    req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Query<OAuth2Request>,
) -> impl Responder {
    oauth2_request_backend(req, data, info).await
}

/// Handles OAuth2 token request
async fn token_get(
    req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Form<OAuth2SignInFull>,
) -> HttpResponse {
    // Validate grant type
    if info.grant_type != "authorization_code" {
        return HttpResponse::BadRequest().body("Only authorization codes are allowed");
    }

    // Find OAuth2 request
    let oauth2_request = match quadrant_oauth2_requests::Entity::find()
        .filter(quadrant_oauth2_requests::Column::ClientId.eq(info.client_id.clone()))
        .filter(quadrant_oauth2_requests::Column::Code.eq(info.code.clone()))
        .one(&data.conn)
        .await
    {
        Ok(Some(request)) => request,
        Ok(None) => return HttpResponse::BadRequest().body("Request does not exist!"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to fetch request: {}", e));
        }
    };

    // Verify client secret
    let parsed_hash = match PasswordHash::new(&oauth2_request.client_secret) {
        Ok(hash) => hash,
        Err(_) => return HttpResponse::InternalServerError().body("Failed to parse password hash"),
    };

    if Argon2::default()
        .verify_password(info.client_secret.as_bytes(), &parsed_hash)
        .is_err()
    {
        return HttpResponse::Unauthorized().body("Invalid client secret");
    }

    // Delete the used request
    if let Err(e) = quadrant_oauth2_requests::Entity::delete_by_id(oauth2_request.code.clone())
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to delete request: {}", e));
    }

    // Validate token
    let claims = match Claims::from_jwt(oauth2_request.token.clone(), req.connection_info().clone())
    {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    // Calculate token expiration
    let expires = DateTime::from_timestamp(claims.exp, 0).unwrap();
    let expires_in = expires.signed_duration_since(Utc::now()).num_seconds();

    // Return successful response
    HttpResponse::Ok()
        .insert_header(("Cache-Control", "no-store"))
        .json(OAuth2SignInFullResponse {
            access_token: oauth2_request.token,
            token_type: "Bearer".to_string(),
            expires_in,
            scope: claims.sub,
            refresh_token: "".to_string(),
        })
}

#[post("/api/v3/account/oauth2/token/access")]
pub async fn oauth2_sign_in_v3(
    req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Form<OAuth2SignInFull>,
) -> impl Responder {
    token_get(req, data, info).await
}
