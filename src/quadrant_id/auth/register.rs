use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use argon2::{Argon2, PasswordHasher};
use lettre::message::{Mailbox, header::ContentType};
use lettre::{Address, Transport};
use lettre::{Message, transport::smtp::authentication::Credentials};
use qntapi::{DEFAULT_QUADRANT_ICON, build_mailer, generate_email_template};
use rand::Rng;
use sea_orm::*;
use serde::{Deserialize, Serialize};
use tokio::task;
use uuid::Uuid;

use crate::WebData;
use crate::quadrant_id::shared::{check_login, check_password};
use crate::util::entities::{quadrant_accounts, quadrant_verifications};

/// Request for initial registration
#[derive(Debug, Clone, Serialize, Deserialize)]
struct RegistrationRequest {
    #[serde(alias = "email")]
    mail: String,
}

/// Confirmation of registration with verification code
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct RegistrationConfirmation {
    email: String,
    login: String,
    name: String,
    password: String,
    verification_code: i32,
}

/// Handles the initial registration request
async fn register_request_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse registration request
    let parse_res = serde_json::from_str::<RegistrationRequest>(&req_body);
    if let Err(_) = parse_res {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let registration_request = parse_res.unwrap();
    let mail = registration_request.mail.clone();

    // Validate email format
    let parsed_email_res = mail.parse::<Address>();
    if let Err(_) = parsed_email_res {
        return HttpResponse::BadRequest().body("Failed to parse email");
    }
    let parsed_email = Mailbox::new(None, parsed_email_res.unwrap());

    // Check if account already exists
    let existing_account = quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::Email.eq(registration_request.mail.to_lowercase()))
        .one(&data.conn)
        .await;

    match existing_account {
        Ok(Some(_)) => return HttpResponse::Conflict().body("Account already exists!"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
        _ => {}
    }

    // Generate verification code
    let mut rng = rand::rng();
    let verification_code = rng.random_range(********..********);

    // Delete any existing verification requests for this email
    let _ = quadrant_verifications::Entity::delete_many()
        .filter(quadrant_verifications::Column::Email.eq(registration_request.mail.clone()))
        .exec(&data.conn)
        .await;

    // Create new verification request
    let new_verification = quadrant_verifications::ActiveModel {
        id: Set(Uuid::now_v7().to_string()),
        email: Set(registration_request.mail.to_lowercase()),
        code: Set(verification_code),
    };

    if let Err(_) = quadrant_verifications::Entity::insert(new_verification)
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body("Failed to create account registration request!");
    }

    // Send verification email
    let email = Message::builder()
        .from(
            "Quadrant ID Creation <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(parsed_email)
        .subject("Your Quadrant ID verification code")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template(
            "Quadrant ID account creation registration code",
            &format!(
                "<h2>Your verification code is</h2> <h1>{}</h1> <p>It is valid for 5 minutes</p>",
                verification_code
            ),
            DEFAULT_QUADRANT_ICON,
        ));

    if let Err(_) = email {
        return HttpResponse::InternalServerError().body("Failed to send verification email");
    }
    let email = email.unwrap();

    let creds = Credentials::new(data.smtp.username.clone(), data.smtp.password.clone());
    let mailer = build_mailer(creds, data.smtp.url.clone());

    // Spawn cleanup task
    let data_clone = data.clone();
    let mail_clone = mail.clone();
    task::spawn(async move {
        tokio::time::sleep(tokio::time::Duration::from_secs(300)).await;

        if let Err(e) = quadrant_verifications::Entity::delete_many()
            .filter(quadrant_verifications::Column::Email.eq(mail_clone))
            .exec(&data_clone.conn)
            .await
        {
            log::error!("Failed to delete verification code: {}", e);
        }
    });

    match mailer.send(&email) {
        Ok(_) => HttpResponse::Accepted().body("Request created!"),
        Err(e) => {
            HttpResponse::InternalServerError().body(format!("Failed to send the email! ({})", e))
        }
    }
}

/// Handles the final registration confirmation
async fn register_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse confirmation request
    let parse_res = serde_json::from_str::<RegistrationConfirmation>(&req_body);
    if let Err(_) = parse_res {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let registration_request = parse_res.unwrap();

    // Validate email format
    let parsed_email_res = registration_request.email.parse::<Address>();
    if let Err(_) = parsed_email_res {
        return HttpResponse::BadRequest().body("Failed to parse email");
    }
    let parsed_email = Mailbox::new(
        Some(registration_request.name.clone()),
        parsed_email_res.unwrap(),
    );

    // Validate password and login
    if let Err(e) = check_password(&registration_request.password) {
        return e;
    }
    if let Err(e) = check_login(&registration_request.login) {
        return e;
    }

    // Verify the verification code
    let verification = quadrant_verifications::Entity::find()
        .filter(quadrant_verifications::Column::Code.eq(registration_request.verification_code))
        .filter(quadrant_verifications::Column::Email.eq(registration_request.email.clone()))
        .one(&data.conn)
        .await;

    let verification = match verification {
        Ok(Some(v)) => v,
        Ok(None) => return HttpResponse::BadRequest().body("Invalid verification code!"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Hash password
    let argon2 = Argon2::default();
    let salt =
        argon2::password_hash::SaltString::generate(&mut argon2::password_hash::rand_core::OsRng);
    let hashed_password = argon2
        .hash_password(registration_request.password.as_bytes(), &salt)
        .unwrap()
        .to_string();

    // Create new account
    let new_account = quadrant_accounts::ActiveModel {
        id: Set(verification.id),
        session_generation: Set(Uuid::now_v7().to_string()),
        username: Set(registration_request.login),
        display_name: Set(registration_request.name),
        pass: Set(hashed_password),
        email: Set(registration_request.email.to_lowercase()),
        otp_enabled: Set(false),
        email_notifications: Set(true),
    };

    if let Err(e) = quadrant_accounts::Entity::insert(new_account)
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to create account: {}", e));
    }
    // Delete the verification code
    if let Err(e) = quadrant_verifications::Entity::delete_many()
        .filter(quadrant_verifications::Column::Code.eq(registration_request.verification_code))
        .filter(quadrant_verifications::Column::Email.eq(registration_request.email.clone()))
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to delete verification code: {}", e));
    }
    // Send welcome email
    let data_clone = data.clone();
    task::spawn(async move {
        let email = Message::builder()
            .from("Quadrant ID Creation <<EMAIL>>".parse().unwrap())
            .to(parsed_email)
            .subject("Your Quadrant ID has been created!")
            .header(ContentType::TEXT_HTML)
            .body(generate_email_template("Welcome to Quadrant ID!", "<h2>Now you can use features such as Quadrant Sync and get more shares in Quadrant Share</h2>", DEFAULT_QUADRANT_ICON));

        if let Ok(email) = email {
            let creds = Credentials::new(
                data_clone.smtp.username.clone(),
                data_clone.smtp.password.clone(),
            );
            let mailer = build_mailer(creds, data_clone.smtp.url.clone());

            if let Err(e) = mailer.send(&email) {
                log::error!("Failed to send welcome email: {}", e);
            }
        }
    });

    HttpResponse::Created().body("Account created successfully!")
}

#[post("/api/v3/account/registration/request")]
pub async fn register_request_v3(
    req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    register_request_backend(req, req_body, data).await
}

#[post("/api/v3/account/registration/confirm")]
pub async fn register_v3(
    req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    register_backend(req, req_body, data).await
}
