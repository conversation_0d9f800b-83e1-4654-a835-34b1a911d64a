use crate::WebData;
use actix_web::{HttpRequest, HttpResponse, Responder, cookie, post, web};
use argon2::{Argon2, PasswordHash, PasswordVerifier};
use jsonwebtoken::{Decod<PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Validation, encode};
use lettre::{
    Message, Transport, message::header::ContentType, transport::smtp::authentication::Credentials,
};
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};

use log::info;
use qntapi::{
    ApiKeySearchFilter, Claims, DEFAULT_QUADRANT_ICON, build_mailer, generate_email_template,
    get_api_keys, send_notification,
};
use serde::{Deserialize, Serialize};
use totp_rs::{Algorithm, Secret, TOTP};
use uuid::Uuid;

use crate::quadrant_id::shared::IPAddressInfo;
use crate::util::entities::{
    quadrant_accounts, quadrant_oauth2_apps, quadrant_oauth2_requests, quadrant_otp,
};

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub struct LoginRequest {
    pub email: Option<String>,
    pub password: Option<String>,
    pub scope: String,
    pub token_duration: i64,
    pub device: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct OAuth2Response {
    code: String,
}

#[derive(Deserialize)]
struct LoginExtraParams {
    client_id: Option<String>,
    auth_token: Option<String>,
    totp_code: Option<i32>,
    secure: Option<bool>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct LoginNotification {
    pub notification_type: String,
    pub simple_message: String,
}

async fn login_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
    info: web::Query<LoginExtraParams>,
) -> HttpResponse {
    // Verify API key
    let api_key_filter = ApiKeySearchFilter {
        product: "account".to_string(),
        scope: "login".to_string(),
    };
    let get_keys_res = get_api_keys(data.as_ref().to_owned(), api_key_filter).await;
    if get_keys_res.is_err() {
        return HttpResponse::InternalServerError()
            .body("Failed to verify your API key (doesn't mean that it's invalid)");
    }
    let authentication_keys: Vec<String> = get_keys_res.unwrap();
    if _req.headers().get("Authorization").is_none()
        || !authentication_keys.contains(
            &_req
                .headers()
                .get("Authorization")
                .unwrap()
                .to_str()
                .unwrap()
                .to_owned(),
        )
    {
        return HttpResponse::Forbidden().body("Your API key is invalid.");
    }

    let token_secret = std::env::var("JWT_SECRET")
        .map_err(|_| HttpResponse::InternalServerError().body("Failed to get the JWT secret"));
    if token_secret.is_err() {
        return token_secret.err().unwrap();
    }
    let token_secret = token_secret.unwrap();
    let auth_token_secret = token_secret.clone();
    let totp_code = info.totp_code;

    // Handle OAuth2 app verification
    let oauth2_app = if info.client_id.is_some() && info.auth_token.is_some() {
        let id = info.client_id.clone().unwrap();

        match quadrant_oauth2_apps::Entity::find()
            .filter(quadrant_oauth2_apps::Column::ClientId.eq(id))
            .one(&data.conn)
            .await
        {
            Ok(Some(app)) => Some(app),
            _ => None,
        }
    } else if (info.client_id.is_none() || info.auth_token.is_none())
        && !(info.client_id.is_none() && info.auth_token.is_none())
    {
        return HttpResponse::BadRequest().body("Invalid client ID or secret");
    } else {
        None
    };

    // Parse login request
    let parse_res = serde_json::from_str::<LoginRequest>(&req_body);
    if parse_res.is_err() && oauth2_app.is_none() {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let sign_in_request = parse_res.unwrap();
    let s_in_request = sign_in_request.clone();

    if oauth2_app.clone().is_none()
        && (sign_in_request.device.is_none()
            || sign_in_request.email.is_none()
            || sign_in_request.password.is_none())
    {
        return HttpResponse::BadRequest().body("Invalid request");
    }

    let app = oauth2_app.clone();
    let secure_login = info.secure;

    // Find account
    let account = match if app.is_none() {
        quadrant_accounts::Entity::find()
            .filter(
                quadrant_accounts::Column::Email.eq(sign_in_request.email.unwrap().to_lowercase()),
            )
            .one(&data.conn)
            .await
    } else {
        let token_info = jsonwebtoken::decode::<Claims>(
            &info.auth_token.clone().unwrap(),
            &DecodingKey::from_secret(token_secret.as_ref()),
            &Validation::default(),
        )
        .unwrap();

        quadrant_accounts::Entity::find()
            .filter(quadrant_accounts::Column::SessionGeneration.eq(token_info.claims.uid))
            .one(&data.conn)
            .await
    } {
        Ok(Some(account)) => account,
        _ => return HttpResponse::BadRequest().body("Account does not exist!"),
    };

    let email_notifications = account.email_notifications;
    let uid = account.id.clone();
    let user_id = uid.clone();

    // Handle TOTP verification
    if account.otp_enabled && totp_code.is_none() && oauth2_app.clone().is_none() {
        return HttpResponse::BadRequest().body("TOTP code is required");
    } else if totp_code.is_some() && account.otp_enabled && oauth2_app.clone().is_none() {
        let totp_code = totp_code.unwrap();

        // Get TOTP settings for the user
        let totp_settings = match quadrant_otp::Entity::find()
            .filter(quadrant_otp::Column::UserId.eq(uid.clone()))
            .one(&data.conn)
            .await
        {
            Ok(Some(settings)) => settings,
            Ok(None) => return HttpResponse::InternalServerError().body("TOTP settings not found"),
            Err(e) => {
                return HttpResponse::InternalServerError()
                    .body(format!("Failed to fetch TOTP settings: {}", e));
            }
        };

        // Initialize TOTP verifier
        let totp = match TOTP::new(
            Algorithm::SHA512,
            7,
            1,
            30,
            Secret::Encoded(totp_settings.secret).to_bytes().unwrap(),
            Some("Quadrant ID".to_string()),
            user_id.clone(),
        ) {
            Ok(totp) => totp,
            Err(e) => {
                return HttpResponse::InternalServerError()
                    .body(format!("Failed to initialize TOTP verifier: {}", e));
            }
        };

        // Verify TOTP code
        match totp.check_current(&totp_code.to_string()) {
            Ok(true) => (), // Code is valid, continue with login
            Ok(false) => return HttpResponse::Forbidden().body("Invalid TOTP code"),
            Err(_) => {
                return HttpResponse::InternalServerError().body("Failed to verify TOTP code");
            }
        }
    }

    let email = account.email.clone();

    // Verify password if not OAuth2
    if oauth2_app.clone().is_none() {
        let login_pass = s_in_request.password.unwrap();
        let parsed_hash = PasswordHash::new(&account.pass)
            .map_err(|_| HttpResponse::InternalServerError().body("Failed to parse password hash"));
        if parsed_hash.is_err() {
            return parsed_hash.err().unwrap();
        }
        let parsed_hash = parsed_hash.unwrap();

        if Argon2::default()
            .verify_password(login_pass.as_bytes(), &parsed_hash)
            .is_err()
        {
            return HttpResponse::Forbidden().body("Invalid password");
        }
    }

    // Get device information
    let device = _req.connection_info();
    let device = match device.realip_remote_addr() {
        Some(addr) => addr,
        None => {
            return HttpResponse::InternalServerError().body("Failed to get the device address");
        }
    };
    let mut ip = device.to_string();

    ip = ip.split(':').collect::<Vec<&str>>()[0].to_string();

    if ip == "127.0.0.1" {
        ip = "**************".to_string();
    }
    let res = reqwest::get(format!("http://ip-api.com/json/{}", ip)).await;
    if res.is_err() {
        return HttpResponse::InternalServerError().body("Failed to get the device country");
    }
    let res = res.unwrap();
    let body = res.text().await.unwrap();
    let country = serde_json::from_str(&body);
    if country.is_err() {
        return HttpResponse::InternalServerError().body("Failed to parse the device country");
    }
    let country: IPAddressInfo = country.unwrap();

    if country.country.clone().to_lowercase() == "russia" {
        return HttpResponse::Forbidden().body(
            "You can't sign into accounts in this country. This is not a bug, do not report it.",
        );
    }

    info!("{:?}", country);
    let claims = Claims {
        cntry: country.country_code.clone(),
        ip,
        sub: sign_in_request.scope,
        exp: (chrono::Utc::now() + chrono::Duration::seconds(sign_in_request.token_duration))
            .timestamp(),
        iat: chrono::Utc::now().timestamp(),
        device: match oauth2_app.clone() {
            Some(app) => app.display_name,
            _ => sign_in_request.device.unwrap(),
        },
        iss: "MrQuantumOFF.DEV API".to_string(),
        uid: account.session_generation.clone(),
        enforce_ip: Some(oauth2_app.clone().is_none()),
    };

    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(auth_token_secret.as_ref()),
    );
    if token.is_err() {
        return HttpResponse::InternalServerError().body("Failed to encode the token");
    }
    let auth_token = token.unwrap();
    let token = auth_token.clone();
    let auth_code = Uuid::now_v7().to_string();

    let oauth_res: Option<OAuth2Response> = if let Some(app) = oauth2_app.clone() {
        // Delete existing OAuth2 requests
        match quadrant_oauth2_requests::Entity::delete_many()
            .filter(quadrant_oauth2_requests::Column::UserId.eq(account.id.clone()))
            .filter(quadrant_oauth2_requests::Column::ClientId.eq(app.client_id.clone()))
            .exec(&data.conn)
            .await
        {
            Ok(_) => (),
            Err(e) => {
                return HttpResponse::InternalServerError()
                    .body(format!("Failed to delete existing OAuth2 requests: {}", e));
            }
        };

        // Create new OAuth2 request
        let new_request = quadrant_oauth2_requests::ActiveModel {
            code: Set(auth_code.clone()),
            client_id: Set(app.client_id.clone()),
            user_id: Set(account.id.clone()),
            token: Set(auth_token.clone()),
            client_secret: Set(app.client_secret.clone()),
        };

        match new_request.insert(&data.conn).await {
            Ok(_) => (),
            Err(e) => {
                return HttpResponse::InternalServerError()
                    .body(format!("Failed to create OAuth2 request: {}", e));
            }
        }

        Some(OAuth2Response { code: auth_code })
    } else {
        None
    };

    if email_notifications {
        let email = match oauth2_app.clone() {
            Some(app) => Message::builder()
                .from("Quadrant ID New app authorized <<EMAIL>>".parse().unwrap())
                .to(format!("me <{}>", email).parse().unwrap())
                .subject(format!("There is a new instance of {} that is logged in with your Quadrant ID!", app.display_name))
                .header(ContentType::TEXT_HTML)
                .body(generate_email_template(&format!("There is a new instance of {} that is logged in with your Quadrant ID!", app.display_name), &format!("<h1>{}</h1>\n\n<p>{}</p>", app.display_name, app.description), &app.image),),
            _ => Message::builder()
                .from("Quadrant ID Sign-in notifications <<EMAIL>>".parse().unwrap())
                .to(format!("me <{}>", email).parse().unwrap())
                .subject("There is a new sign-in into your Quadrant ID!")
                .header(ContentType::TEXT_HTML)
                .body(generate_email_template(&format!("New sign in to your account from {}", country.country), &format!(
                    "<ul><li>The request was done from {}/{}</li>\n<li>The IP address of the device logged in is {}</li>\n<li>The ISP is {}</li></ul> <h3>If this wasn't you, consider changing your password and resetting your active sessions.</h3>",
                    country.city, country.country, country.ip, country.isp
                ), DEFAULT_QUADRANT_ICON),),
        };

        if email.is_err() {
            return HttpResponse::InternalServerError().body("Failed to create notification email");
        }
        let email = email.unwrap();

        let creds = Credentials::new(data.smtp.username.clone(), data.smtp.password.clone());
        let mailer = build_mailer(creds, data.smtp.url.clone());

        return match mailer.send(&email) {
            Ok(_) => match oauth_res {
                Some(oauth2_response) => HttpResponse::Ok().json(oauth2_response),
                None => HttpResponse::Accepted().body(token),
            },
            Err(e) => {
                HttpResponse::PartialContent().body(format!("Failed to send the email ({})", e))
            }
        };
    }

    let notification = match oauth2_app.clone() {
        Some(app) => LoginNotification {
            notification_type: "login".to_string(),
            simple_message: format!("New instance of {} authorized", app.display_name),
        },
        _ => LoginNotification {
            notification_type: "login".to_string(),
            simple_message: format!(
                "New login from {},{}. If this wasn't you consider changing your password and resetting your active sesssions.",
                country.city, country.country_code
            ),
        },
    };

    let res = send_notification(user_id, notification, data).await;
    let mut token_cookie = cookie::Cookie::build("quadrant_id_token", &token)
        .domain("mrquantumoff.dev")
        .domain("api.mrquantumoff.dev")
        .max_age(cookie::time::Duration::seconds(
            sign_in_request.token_duration,
        ))
        .secure(!cfg!(debug_assertions));

    if cfg!(debug_assertions) {
        token_cookie = token_cookie.domain("localhost");
    }

    match res {
        Ok(_) => match oauth_res {
            Some(oauth2_response) => HttpResponse::Ok().json(oauth2_response),
            None => match secure_login {
                Some(true) => HttpResponse::Accepted()
                    .cookie(token_cookie.finish())
                    .body("OK"),
                _ => HttpResponse::Accepted()
                    .cookie(token_cookie.finish())
                    .body(token),
            },
        },
        Err(e) => {
            HttpResponse::PartialContent().body(format!("Failed to send the notification ({})", e))
        }
    }
}

#[post("/api/v3/account/login")]
pub async fn login_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
    info: web::Query<LoginExtraParams>,
) -> impl Responder {
    login_backend(_req, req_body, data, info).await
}
