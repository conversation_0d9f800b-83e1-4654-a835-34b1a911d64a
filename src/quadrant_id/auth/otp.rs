use actix_web::{HttpRequest, HttpResponse, Responder, delete, post, web};
use sea_orm::*;
use serde::{Deserialize, Serialize};
use totp_rs::{Algorithm, Secret, TOTP};

use crate::WebData;
use crate::util::entities::{quadrant_accounts, quadrant_otp};
use qntapi::verify_jwt;

/// Response structure for OTP setup
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OTPEnableResponse {
    pub url: String,
    pub image: String,
}

/// Structure for OTP code confirmation
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OTPConfirmation {
    pub code: i32,
}

/// Sets up TOTP (Time-based One-Time Password) for a user
async fn totp_setup_backend(_req: HttpRequest, data: web::Data<WebData>) -> HttpResponse {
    // Verify JWT and check permissions
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("security_changes") {
        return HttpResponse::Forbidden().body("You can't modify OTP.");
    }

    // Get user account
    let user = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid.to_string()))
        .one(&data.conn)
        .await
    {
        Ok(Some(user)) => user,
        Ok(None) => return HttpResponse::NotFound().body("Account not found"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to get the account: {}", e));
        }
    };

    // Check if OTP is already enabled
    if user.otp_enabled {
        return HttpResponse::NotModified().body("OTP is already enabled");
    }

    // Generate new TOTP secret and setup
    let secret = Secret::default();
    let totp = match TOTP::new(
        Algorithm::SHA512,
        7,
        1,
        30,
        secret.to_encoded().to_bytes().unwrap(),
        Some("Quadrant ID".to_string()),
        user.id.clone(),
    ) {
        Ok(totp) => totp,
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to generate OTP: {}", e));
        }
    };

    let qr_code = totp.get_qr_base64().unwrap_or("RVJST1I=".to_string());
    let otpurl = totp.get_url();
    let secret_string = secret.to_encoded().to_string();

    // Delete any existing OTP records for this user
    if let Err(e) = quadrant_otp::Entity::delete_many()
        .filter(quadrant_otp::Column::UserId.eq(user.id.clone()))
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to clean up old OTP records: {}", e));
    }

    // Create new OTP record
    let otp_model = quadrant_otp::ActiveModel {
        user_id: Set(user.id.clone()),
        secret: Set(secret_string),
    };

    if let Err(e) = quadrant_otp::Entity::insert(otp_model)
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to save OTP settings: {}", e));
    }

    // Return success response
    HttpResponse::Ok().json(OTPEnableResponse {
        url: otpurl,
        image: qr_code,
    })
}

#[post("/api/v3/account/totp/setup")]
pub async fn otp_setup_v3(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    totp_setup_backend(_req, data).await
}

/// Verifies and enables TOTP for a user
async fn otp_verification_backend(
    _req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Query<OTPConfirmation>,
) -> HttpResponse {
    // Verify JWT and check permissions
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("security_changes") {
        return HttpResponse::Forbidden().body("You can't modify OTP.");
    }

    // Get user account and OTP settings
    let user = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid.to_string()))
        .one(&data.conn)
        .await
    {
        Ok(Some(user)) => user,
        Ok(None) => return HttpResponse::NotFound().body("Account not found"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to get the account: {}", e));
        }
    };

    if user.otp_enabled {
        return HttpResponse::NotModified().body("OTP is already enabled");
    }

    // Get OTP secret
    let otp = match quadrant_otp::Entity::find()
        .filter(quadrant_otp::Column::UserId.eq(user.id.clone()))
        .one(&data.conn)
        .await
    {
        Ok(Some(otp)) => otp,
        Ok(None) => return HttpResponse::NotFound().body("OTP setup not found"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to get OTP settings: {}", e));
        }
    };

    // Verify TOTP code
    let totp = match TOTP::new(
        Algorithm::SHA512,
        7,
        1,
        30,
        Secret::Encoded(otp.secret).to_bytes().unwrap(),
        Some("Quadrant ID".to_string()),
        user.id.clone(),
    ) {
        Ok(totp) => totp,
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to generate OTP: {}", e));
        }
    };

    match totp.check_current(&info.code.to_string()) {
        Ok(true) => (),
        Ok(false) => return HttpResponse::Unauthorized().body("Invalid OTP code"),
        Err(_) => return HttpResponse::InternalServerError().body("Failed to verify TOTP code"),
    }

    // Enable OTP for the user
    let mut user_active: quadrant_accounts::ActiveModel = user.into();
    user_active.otp_enabled = Set(true);

    if let Err(e) = user_active.update(&data.conn).await {
        return HttpResponse::InternalServerError().body(format!("Failed to enable OTP: {}", e));
    }

    HttpResponse::Ok().body("OK")
}

#[post("/api/v3/account/totp/verify")]
pub async fn otp_verification_v3(
    _req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Query<OTPConfirmation>,
) -> impl Responder {
    otp_verification_backend(_req, data, info).await
}

/// Handles the disabling of TOTP for a user
async fn otp_disable_backend(
    _req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Query<OTPConfirmation>,
) -> HttpResponse {
    // Verify JWT and check permissions
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("security_changes") {
        return HttpResponse::Forbidden().body("You can't modify OTP.");
    }

    // Get user account
    let user = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid.to_string()))
        .one(&data.conn)
        .await
    {
        Ok(Some(user)) => user,
        Ok(None) => return HttpResponse::NotFound().body("Account not found"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to get the account: {}", e));
        }
    };

    if !user.otp_enabled {
        return HttpResponse::NotModified().body("OTP is already disabled");
    }

    // Get OTP settings
    let otp = match quadrant_otp::Entity::find()
        .filter(quadrant_otp::Column::UserId.eq(user.id.clone()))
        .one(&data.conn)
        .await
    {
        Ok(Some(otp)) => otp,
        Ok(None) => return HttpResponse::NotFound().body("OTP settings not found"),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to get OTP settings: {}", e));
        }
    };

    // Verify TOTP code before disabling
    let totp = match TOTP::new(
        Algorithm::SHA512,
        7,
        1,
        30,
        Secret::Encoded(otp.secret).to_bytes().unwrap(),
        Some("Quadrant ID".to_string()),
        user.id.clone(),
    ) {
        Ok(totp) => totp,
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to generate OTP: {}", e));
        }
    };

    match totp.check_current(&info.code.to_string()) {
        Ok(true) => (),
        Ok(false) => return HttpResponse::Unauthorized().body("Invalid OTP code"),
        Err(_) => return HttpResponse::InternalServerError().body("Failed to verify TOTP code"),
    }

    // Disable OTP in a transaction
    let transaction_result = data
        .conn
        .transaction::<_, (), DbErr>(|txn| {
            Box::pin(async move {
                let uid = user.id.clone();

                // Update user account to disable OTP
                let mut user_active: quadrant_accounts::ActiveModel = user.into();
                user_active.otp_enabled = Set(false);
                user_active.update(txn).await?;

                // Delete OTP settings
                quadrant_otp::Entity::delete_many()
                    .filter(quadrant_otp::Column::UserId.eq(uid))
                    .exec(txn)
                    .await?;

                Ok(())
            })
        })
        .await;

    match transaction_result {
        Ok(_) => HttpResponse::Ok().body("OK"),
        Err(e) => HttpResponse::InternalServerError().body(format!("Failed to disable OTP: {}", e)),
    }
}

#[delete("/api/v3/account/totp/disable")]
pub async fn otp_disable_v3(
    _req: HttpRequest,
    data: web::Data<WebData>,
    info: web::Query<OTPConfirmation>,
) -> impl Responder {
    otp_disable_backend(_req, data, info).await
}
