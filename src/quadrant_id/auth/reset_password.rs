use actix_web::{HttpRe<PERSON>, HttpResponse, Responder, patch, web};
use argon2::{Argon2, PasswordHasher, password_hash::SaltString};
use lettre::{Message, Transport, message::header::ContentType};
use log::error;
use qntapi::{DEFAULT_QUADRANT_ICON, build_mailer, generate_email_template};
use rand::Rng;
use sea_orm::{IntoActiveModel, Set, TransactionTrait, prelude::*};
use serde::{Deserialize, Serialize};
use tokio::task;

use crate::WebData;
use crate::quadrant_id::shared::check_password;
use crate::util::entities::{quadrant_accounts, quadrant_id_reset_password_requests};

/// Request for password reset initiation
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct ResetRequest {
    pub email: String,
}

/// Confirmation of password reset with verification code
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ResetConfirmation {
    pub email: String,
    pub code: i32,
    pub password: String,
}

/// Handles the initial password reset request
async fn request_password_reset_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse reset request
    let parse_res = serde_json::from_str::<ResetRequest>(&req_body);
    if let Err(_) = parse_res {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let reset_req = parse_res.unwrap();
    let r_req = reset_req.clone();
    let mail = reset_req.email.clone();

    // Check if account exists
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::Email.eq(reset_req.email.clone()))
        .one(&data.conn)
        .await
    {
        Ok(Some(acc)) => acc,
        Ok(None) => return HttpResponse::BadRequest().body("Account does not exist!"),
        Err(e) => {
            return HttpResponse::BadRequest()
                .body(format!("Account existence check failed: {}", e));
        }
    };

    // Generate verification code
    let mut rng = rand::rng();
    let verification_code = rng.random_range(********..********);

    // Delete any existing reset requests
    let _ = quadrant_id_reset_password_requests::Entity::delete_many()
        .filter(quadrant_id_reset_password_requests::Column::AccountId.eq(account.id.clone()))
        .exec(&data.conn)
        .await;

    // Create new reset request
    let new_reset_request = quadrant_id_reset_password_requests::ActiveModel {
        account_id: Set(account.id.clone()),
        email: Set(account.email.clone()),
        code: Set(verification_code),
    };

    if let Err(e) = quadrant_id_reset_password_requests::Entity::insert(new_reset_request)
        .exec(&data.conn)
        .await
    {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to create reset request: {}", e));
    }

    // Create and send email
    let email = Message::builder()
        .from("Quadrant ID Password reset <<EMAIL>>".parse().unwrap())
        .to(format!("me <{}>", mail).parse().unwrap())
        .subject("Your Quadrant ID verification code")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template("Quadrant ID Password reset request", &format!(
            "<h2>If you want to change your password, your verification code is</h2> <h1>{}</h1> <p>It is valid for 15 minutes.</p>
            <h2>If you didn't want to change your password, this means that someone has either \"accidentally\" \
            typed in your email or someone is trying to hack you!</h2>",
            verification_code
        ), DEFAULT_QUADRANT_ICON));

    if let Err(_) = email {
        return HttpResponse::InternalServerError().body("Failed to create verification email");
    }
    let email = email.unwrap();

    let creds = lettre::transport::smtp::authentication::Credentials::new(
        data.smtp.username.clone(),
        data.smtp.password.clone(),
    );

    let mailer = build_mailer(creds, data.smtp.url.clone());

    // Spawn cleanup task
    let data_clone = data.clone();
    task::spawn(async move {
        tokio::time::sleep(tokio::time::Duration::from_secs(900)).await;

        if let Err(e) = quadrant_id_reset_password_requests::Entity::delete_many()
            .filter(quadrant_id_reset_password_requests::Column::Email.eq(r_req.email))
            .exec(&data_clone.conn)
            .await
        {
            error!("Failed to delete reset request: {}", e);
        }
    });

    // Send email
    match mailer.send(&email) {
        Ok(_) => HttpResponse::Accepted().body("Request created!"),
        Err(e) => {
            HttpResponse::InternalServerError().body(format!("Failed to send the email! ({})", e))
        }
    }
}

/// Handles the password reset confirmation
async fn reset_password_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse confirmation request
    let parse_res = serde_json::from_str::<ResetConfirmation>(&req_body);
    if let Err(_) = parse_res {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let reset_confirmation = parse_res.unwrap();
    let mail = reset_confirmation.email.clone();
    let user_mail = mail.clone();

    // Validate new password
    if let Err(response) = check_password(&reset_confirmation.password) {
        return response;
    }

    // Hash new password
    let argon2 = Argon2::default();
    let salt = SaltString::generate(&mut argon2::password_hash::rand_core::OsRng);
    let hashed_password = argon2
        .hash_password(reset_confirmation.password.as_bytes(), &salt)
        .unwrap()
        .to_string();
    let reset_request = match quadrant_id_reset_password_requests::Entity::find()
        .filter(quadrant_id_reset_password_requests::Column::Email.eq(mail.clone()))
        .filter(quadrant_id_reset_password_requests::Column::Code.eq(reset_confirmation.code))
        .one(&data.conn)
        .await
    {
        Ok(Some(req)) => req,
        Ok(None) => {
            return HttpResponse::BadRequest().body("Invalid reset data provided!");
        }
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to verify reset request: {}", e));
        }
    };
    // Find reset request and update password
    let res = data.conn.transaction::<_, (), DbErr>(|txn| {
        Box::pin(async move {
            // Update password
            let mut account = match quadrant_accounts::Entity::find()
                .filter(quadrant_accounts::Column::Id.eq(reset_request.account_id))
                .one(txn)
                .await?
            {
                Some(acc) => acc.into_active_model(),
                None => return Err(DbErr::Custom("Account not found".to_string())),
            };

            account.pass = Set(hashed_password);

            account.update(txn).await?;

            // Delete reset request
            quadrant_id_reset_password_requests::Entity::delete_many()
                .filter(quadrant_id_reset_password_requests::Column::Email.eq(mail.clone()))
                .exec(txn)
                .await?;
            Ok(())
        })
    });

    if let Err(e) = res.await {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to reset password: {}", e));
    }

    // Send confirmation email
    let email = Message::builder()
        .from(
            "Quadrant ID Password change <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(format!("me <{}>", user_mail).parse().unwrap())
        .subject("Your Quadrant ID password was reset.")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template(
            "Your Quadrant ID password was reset.",
            "<h2>Your Quadrant ID password was reset.</h2>",
            DEFAULT_QUADRANT_ICON,
        ));

    if let Err(_) = email {
        return HttpResponse::InternalServerError().body("Failed to create confirmation email");
    }
    let email = email.unwrap();

    let creds = lettre::transport::smtp::authentication::Credentials::new(
        data.smtp.username.clone(),
        data.smtp.password.clone(),
    );

    let mailer = build_mailer(creds, data.smtp.url.clone());

    match mailer.send(&email) {
        Ok(_) => HttpResponse::Ok().body("Password reset successfully!"),
        Err(e) => HttpResponse::InternalServerError().body(format!(
            "Password reset successful but failed to send confirmation email: {}",
            e
        )),
    }
}

#[patch("/api/v3/account/password/reset/request")]
pub async fn request_password_reset_v3(
    req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    request_password_reset_backend(req, req_body, data).await
}
#[patch("/api/v3/account/password/reset")]
pub async fn reset_password_v3(
    req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    reset_password_backend(req, req_body, data).await
}
