use actix_web::{HttpRequest, HttpResponse, Responder, delete, web};
use argon2::{Argon2, PasswordHash, PasswordVerifier};
use lettre::{
    Message, Transport, message::header::ContentType, transport::smtp::authentication::Credentials,
};
use qntapi::{
    DEFAULT_QUADRANT_ICON, WebData, build_mailer, generate_email_template,
    get_share_sync_limit_and_groups,
};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use serde::{Deserialize, Serialize};

use crate::util::entities::{prelude::*, quadrant_accounts};

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct DeletionRequest {
    pub email: String,
    pub password: String,
}

async fn delete_account_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse the deletion request
    let parse_res = serde_json::from_str::<DeletionRequest>(&req_body);
    if parse_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let del_request = parse_res.unwrap();

    // Find the account in the database
    let account = match QuadrantAccounts::find()
        .filter(quadrant_accounts::Column::Email.eq(del_request.email.to_lowercase()))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::BadRequest().body("Account does not exist!"),
        Err(e) => return HttpResponse::BadRequest().body(format!("Database error: {}", e)),
    };

    let password = account.pass.clone();
    let mail = account.email.clone().to_lowercase();

    // Get user groups and limits
    let (groups, _, _) = get_share_sync_limit_and_groups(account.id.clone(), &data.conn).await;

    // Check if user has preventDeletion privilege
    if groups
        .iter()
        .any(|x| x.privileges.contains(&"preventDeletion".to_string()))
    {
        return HttpResponse::Forbidden().body("Cancel your subscription first!");
    }

    // Verify password
    let parsed_hash = match PasswordHash::new(&password) {
        Ok(hash) => hash,
        Err(_) => return HttpResponse::InternalServerError().body("Failed to parse password hash"),
    };

    if Argon2::default()
        .verify_password(del_request.password.as_bytes(), &parsed_hash)
        .is_err()
    {
        return HttpResponse::Forbidden().body("Invalid password!");
    }

    // Delete the account
    match QuadrantAccounts::delete_many()
        .filter(quadrant_accounts::Column::Email.eq(del_request.email.to_lowercase()))
        .exec(&data.conn)
        .await
    {
        Ok(_) => (),
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to delete account: {}", e));
        }
    }

    // Send goodbye email
    let email = Message::builder()
        .from(
            "Quadrant ID Deletion <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(format!("me <{}>", mail).parse().unwrap())
        .subject("Your Quadrant ID has been deleted!")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template(
            "You're leaving Quadrant ID",
            "It truly is sad to see you go.",
            DEFAULT_QUADRANT_ICON,
        ))
        .map_err(|_| HttpResponse::InternalServerError().body("Failed to create goodbye email"));
    if email.is_err() {
        return email.err().unwrap();
    }
    let email = email.unwrap();

    let creds = Credentials::new(data.smtp.username.clone(), data.smtp.password.clone());
    let mailer = build_mailer(creds, data.smtp.url.clone());

    // Send the email
    match mailer.send(&email) {
        Ok(_) => HttpResponse::Accepted().body("Account deleted!"),
        Err(e) => HttpResponse::PartialContent().body(format!(
            "Failed to send the email, but account deletion was successful ({})",
            e
        )),
    }
}

#[delete("/api/v3/account/delete")]
pub async fn delete_account_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    delete_account_backend(_req, req_body, data).await
}
