use actix_web::{HttpRequest, HttpResponse, Responder, get, web};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use serde::Serialize;

use crate::util::entities::{prelude::*, quadrant_accounts, quadrant_notifications};
use qntapi::{WebData, verify_jwt};

#[derive(Clone, serde::Serialize, serde::Deserialize, Debug, PartialEq)]
pub struct PublicNotification {
    pub notification_id: String,
    pub user_id: String,
    pub message: String,
    pub created_at: i64,
    pub read: bool,
}

impl From<quadrant_notifications::Model> for PublicNotification {
    fn from(value: quadrant_notifications::Model) -> Self {
        PublicNotification {
            notification_id: value.notification_id,
            user_id: value.user_id,
            message: value.message,
            created_at: value.created_at.timestamp(),
            read: value.read,
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct PublicInfo {
    pub id: String,
    pub name: String,
    pub login: String,
    pub quadrant_sync_limit: i64,
    pub quadrant_share_limit: i64,
    pub email: Option<String>,
    pub otp_enabled: Option<bool>,
    pub email_notifications_enabled: bool,
    pub notifications: Option<Vec<PublicNotification>>,
    pub groups: Vec<qntapi::util::entities::user_groups::Model>,
}

impl
    From<(
        quadrant_accounts::Model,
        i64,
        i64,
        Vec<qntapi::util::entities::user_groups::Model>,
    )> for PublicInfo
{
    fn from(
        acc: (
            quadrant_accounts::Model,
            i64,
            i64,
            Vec<qntapi::util::entities::user_groups::Model>,
        ),
    ) -> Self {
        PublicInfo {
            id: acc.0.id,
            name: acc.0.display_name,
            login: acc.0.username,
            quadrant_sync_limit: acc.2,
            quadrant_share_limit: acc.1,
            email: Some(acc.0.email),
            otp_enabled: Some(acc.0.otp_enabled),
            email_notifications_enabled: acc.0.email_notifications,
            notifications: None,
            groups: acc.3,
        }
    }
}

async fn get_user_info_backend(_req: HttpRequest, data: web::Data<WebData>) -> HttpResponse {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("user_data") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    let uid = claims.uid;

    // Find account
    let account = match QuadrantAccounts::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(uid.to_string()))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::BadRequest().body("Account does not exist!"),
        Err(e) => {
            return HttpResponse::InternalServerError().body(format!("Database error: {}", e));
        }
    };

    // Get notifications
    let notifications = match QuadrantNotifications::find()
        .filter(quadrant_notifications::Column::UserId.eq(account.id.clone()))
        .all(&data.conn)
        .await
    {
        Ok(notifications) => notifications,
        Err(e) => {
            return HttpResponse::InternalServerError()
                .body(format!("Failed to fetch notifications: {}", e));
        }
    };

    // Get user groups and limits
    let (groups, share_limit, sync_limit) =
        qntapi::get_share_sync_limit_and_groups(account.id.clone(), &data.conn).await;

    // Build response
    let mut public_info = PublicInfo::from((account, share_limit, sync_limit, groups));
    public_info.notifications = Some(
        notifications
            .iter()
            .map(|n| PublicNotification::from(n.to_owned()))
            .collect(),
    );

    // Remove sensitive data based on permissions
    if !claims.sub.contains("security_changes") {
        public_info.otp_enabled = None;
    }
    if !claims.sub.contains("notifications") {
        public_info.notifications = None;
    }

    HttpResponse::Ok().json(public_info)
}

/// Get user account information
///
/// # API Endpoint
/// GET /api/v3/account/info/get
///
/// # Authorization
/// Requires a valid JWT token with "user_data" subject
///
/// # Returns
/// Public information about the authenticated user's account including:
/// - Basic account details
/// - Sync and share limits
/// - Notifications (if authorized)
/// - User groups
#[get("/api/v3/account/info/get")]
pub async fn get_info_v3(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    get_user_info_backend(_req, data).await
}
