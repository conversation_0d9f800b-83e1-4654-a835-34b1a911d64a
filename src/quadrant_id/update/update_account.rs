use crate::util::entities::{prelude::*, quadrant_accounts};
use actix_web::{HttpRequest, HttpResponse, Responder, patch, web};
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier, password_hash::SaltString};
use lettre::{
    Message, Transport, message::header::ContentType, transport::smtp::authentication::Credentials,
};
use qntapi::{
    DEFAULT_QUADRANT_ICON, WebData, build_mailer, generate_email_template, send_notification,
    verify_jwt,
};
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::quadrant_id::shared::{check_login, check_password};

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct UpdateRequest {
    pub password: String,
    pub new_password: Option<String>,
    pub name: Option<String>,
    pub login: Option<String>,
    pub reset_sessions: Option<bool>,
    pub email_notifications: Option<bool>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct AccountUpdateNotification {
    pub notification_type: String,
    pub affected_fields: Vec<String>,
    pub simple_message: String,
}

async fn update_account_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse request body
    let parse_res = serde_json::from_str::<UpdateRequest>(&req_body);
    if parse_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }

    // Validate token and user agent
    let token = _req.headers().get("Authorization");
    let user_agent = _req.headers().get("User-Agent");

    if token.is_none() && user_agent.is_some() {
        let user_agent = user_agent.unwrap();
        if user_agent
            .to_str()
            .unwrap()
            .contains("mrquantumoff/quadrant")
        {
            return HttpResponse::Forbidden().body(
                "Please use the Advanced Settings option. Due to security reasons, your version of Quadrant can't edit the data.",
            );
        } else {
            return HttpResponse::Forbidden().body("No token specified");
        }
    } else if token.is_none() && user_agent.is_none() {
        return HttpResponse::Forbidden().body("No token specified");
    }

    // Verify JWT token
    let claims = verify_jwt(_req);
    if claims.is_err() {
        return HttpResponse::Unauthorized().body(claims.err().unwrap().to_string());
    }
    let claims = claims.unwrap();

    if !claims.sub.contains("user_data") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    let update_request = parse_res.unwrap();
    let sid = claims.uid;

    // Fetch account from database
    let account = QuadrantAccounts::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(sid.clone()))
        .one(&data.conn)
        .await;

    if account.is_err() {
        return HttpResponse::BadRequest().body(format!(
            "Account existence check fail 1: {}",
            account.err().unwrap()
        ));
    }

    let account = account.unwrap();
    if account.is_none() {
        return HttpResponse::BadRequest().body("Account does not exist!");
    }

    let mut acc_data = account.unwrap();
    let old_acc_data = acc_data.clone();

    // Verify current password
    let parsed_hash = PasswordHash::new(&acc_data.pass).expect("Failed to parse the password hash");
    let invalid_pass = Argon2::default()
        .verify_password(update_request.password.as_bytes(), &parsed_hash)
        .is_err();
    if invalid_pass {
        return HttpResponse::Forbidden().body("Invalid password");
    }

    // Track modifications
    let mut is_password_modified = false;
    let mut is_login_modified = false;
    let mut is_name_modified = false;
    let mut are_sessions_affected = false;
    let mut are_email_notifications_affected = false;

    // Create ActiveModel for updates
    let mut account_active: quadrant_accounts::ActiveModel = acc_data.clone().into();

    // Handle password update
    if let Some(new_password) = update_request.new_password {
        let res = check_password(&new_password);
        is_password_modified = true;
        if res.is_err() {
            return res.err().unwrap();
        }
        let argon2 = Argon2::default();
        let salt = SaltString::generate(&mut argon2::password_hash::rand_core::OsRng);
        let bytes_password = new_password.as_bytes();
        let hashed_password = argon2
            .hash_password(bytes_password, &salt)
            .unwrap()
            .to_string();
        account_active.pass = Set(hashed_password.clone());
        acc_data.pass = hashed_password;
    }

    // Handle login update
    if let Some(login) = update_request.login {
        let res = check_login(&login);
        if res.is_err() {
            return res.err().unwrap();
        }
        account_active.username = Set(login.clone());
        acc_data.username = login;
        is_login_modified = true;
    }

    // Handle display name update
    if let Some(name) = update_request.name {
        is_name_modified = true;
        account_active.display_name = Set(name.clone());
        acc_data.display_name = name;
    }

    // Handle email notifications update
    if let Some(new_email_notifications) = update_request.email_notifications {
        are_email_notifications_affected = true;
        account_active.email_notifications = Set(new_email_notifications);
        acc_data.email_notifications = new_email_notifications;
    }

    // Handle session reset
    if let Some(reset_s) = update_request.reset_sessions {
        if reset_s {
            let new_session = Uuid::now_v7().to_string();
            account_active.session_generation = Set(new_session.clone());
            acc_data.session_generation = new_session;
            are_sessions_affected = true;
        }
    }

    if old_acc_data == acc_data {
        return HttpResponse::BadRequest().body("Nothing to update");
    }

    // Update account in database
    let update_result = account_active.update(&data.conn).await;
    if update_result.is_err() {
        return HttpResponse::InternalServerError().body(format!(
            "Failed to update account: {}",
            update_result.err().unwrap()
        ));
    }

    // Prepare notification message
    let mut updated_fields = "".to_owned();
    if is_login_modified {
        updated_fields.push_str("<li>username</li>\n");
    }
    if is_name_modified {
        updated_fields.push_str("<li>display name</li>\n");
    }
    if is_password_modified {
        updated_fields.push_str("<li>password</li>\n");
    }
    if are_sessions_affected {
        updated_fields.push_str("<li>your active sessions were invalidated</li>\n");
    }
    if are_email_notifications_affected {
        updated_fields.push_str(&format!(
            "<li>email notifications are now set to {}</li>\n",
            acc_data.email_notifications
        ));
    }
    updated_fields.replace_range(updated_fields.len() - 2..updated_fields.len() - 1, "");

    let html_conts = generate_email_template(
        "Quadrant ID Updated",
        &format!(
            "<h2>Your account data was updated!</h2> <p>The following fields were updated:</p> <ul>\n{}\n</ul>",
            updated_fields
        ),
        DEFAULT_QUADRANT_ICON,
    );

    #[cfg(debug_assertions)]
    {
        log::info!("{}", &html_conts);
    }

    // Send email notification if enabled
    if old_acc_data.email_notifications {
        let email = Message::builder()
            .from(
                "Quadrant ID Update <<EMAIL>>"
                    .parse()
                    .unwrap(),
            )
            .to(format!("me <{}>", acc_data.email).parse().unwrap())
            .subject("Your Quadrant ID has been updated!")
            .header(ContentType::TEXT_HTML)
            .body(html_conts.clone());

        if email.is_err() {
            return HttpResponse::InternalServerError().body("Failed to send goodbye email");
        }
        let _email = email.unwrap();

        let creds = Credentials::new(data.smtp.username.clone(), data.smtp.password.clone());
        let _mailer = build_mailer(creds, data.smtp.url.clone());

        #[cfg(debug_assertions)]
        {
            return HttpResponse::Accepted().body(html_conts);
        }

        #[allow(unreachable_code)]
        return match _mailer.send(&_email) {
            Ok(_) => HttpResponse::Accepted().body("Account updated!"),
            Err(e) => HttpResponse::PartialContent().body(format!(
                "Failed to send the email, but account update was successful ({})",
                e
            )),
        };
    }

    // Send in-app notification
    let res = send_notification(
        old_acc_data.id,
        AccountUpdateNotification {
            affected_fields: updated_fields.split(", ").map(|x| x.to_string()).collect(),
            notification_type: "account_update".to_string(),
            simple_message: updated_fields,
        },
        data,
    )
    .await;

    match res {
        Ok(_) => HttpResponse::Accepted().body("Account updated!"),
        Err(e) => HttpResponse::InternalServerError().body(format!(
            "Failed to send notification, but account update was successful ({})",
            e
        )),
    }
}

#[patch("/api/v3/account/data/update")]
pub async fn update_data_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    update_account_backend(_req, req_body, data).await
}
