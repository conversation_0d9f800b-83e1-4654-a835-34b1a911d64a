use crate::util::entities::{prelude::*, quadrant_accounts, quadrant_email_updates};
use actix_web::{HttpRequest, HttpResponse, Responder, patch, web};
use argon2::{Argon2, PasswordHash, PasswordVerifier};
use lettre::{
    Address, Message, Transport, message::header::ContentType,
    transport::smtp::authentication::Credentials,
};
use log::error;
use qntapi::{DEFAULT_QUADRANT_ICON, WebData, build_mailer, generate_email_template};
use rand::Rng;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct UpdateRequest {
    pub old_email: String,
    pub password: String,
    pub new_email: String,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct UpdateConfirmation {
    pub new_email: String,
    pub old_email: String,
    pub old_code: i32,
    pub new_code: i32,
}

async fn update_email_request_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse request body
    let parse_res = serde_json::from_str::<UpdateRequest>(&req_body);
    if parse_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let upd_request = parse_res.unwrap();
    let update_request = upd_request.clone();

    let old_mail = upd_request.old_email.clone();
    let new_mail = upd_request.new_email.clone();

    // Validate email addresses
    let parsed_old_email_res = old_mail.parse::<Address>();
    if parsed_old_email_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse email");
    }
    let parsed_old_email = lettre::message::Mailbox::new(None, parsed_old_email_res.unwrap());

    let parsed_new_email_res = new_mail.parse::<Address>();
    if parsed_new_email_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse email");
    }
    let parsed_new_email = lettre::message::Mailbox::new(None, parsed_new_email_res.unwrap());

    // Check if account exists
    let account = QuadrantAccounts::find()
        .filter(quadrant_accounts::Column::Email.eq(upd_request.old_email.clone()))
        .one(&data.conn)
        .await;

    if account.is_err() {
        return HttpResponse::BadRequest().body(format!(
            "Account existence check fail 1: {}",
            account.err().unwrap()
        ));
    }

    let account = account.unwrap();
    if account.is_none() {
        return HttpResponse::BadRequest().body("Account does not exist!");
    }

    let acc_data = account.unwrap();

    // Verify password
    let parsed_hash = PasswordHash::new(&acc_data.pass).expect("Failed to parse the password hash");
    let invalid_pass = Argon2::default()
        .verify_password(update_request.password.as_bytes(), &parsed_hash)
        .is_err();
    if invalid_pass {
        return HttpResponse::Forbidden().body("Invalid password");
    }

    // Generate verification codes
    let mut rng = rand::rng();
    let verification_code_old = rng.random_range(********..********);
    let verification_code_new = rng.random_range(********..********);

    // Delete existing email update requests
    let delete_result = QuadrantEmailUpdates::delete_many()
        .filter(quadrant_email_updates::Column::OldEmail.eq(update_request.old_email.clone()))
        .exec(&data.conn)
        .await;

    if let Err(e) = delete_result {
        error!("Failed to delete existing email update requests: {}", e);
    }

    // Create new email update request
    let new_update = quadrant_email_updates::ActiveModel {
        new_code: Set(verification_code_new),
        old_code: Set(verification_code_old),
        new_email: Set(update_request.new_email.clone().to_lowercase()),
        old_email: Set(update_request.old_email.clone().to_lowercase()),
    };

    if let Err(e) = new_update.insert(&data.conn).await {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to create email update request: {}", e));
    }

    // Prepare email messages
    let old_email_request = Message::builder()
        .from(
            "Quadrant ID Email change <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(parsed_old_email)
        .subject("Your Quadrant ID verification code")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template("Quadrant ID Email change request", &format!(
            "<h2>If you want to change your email, your verification code is: </h2> <h1>{}<h1> <p>It is valid for 15 minutes.</p><h1>If you didn't want to change your email, CHANGE YOUR PASSWORD, NOW.</h1>",
            verification_code_old
        ), DEFAULT_QUADRANT_ICON));

    if old_email_request.is_err() {
        return HttpResponse::InternalServerError().body("Failed to send verification email");
    }
    let old_email_request = old_email_request.unwrap();

    let new_email_request = Message::builder()
        .from(
            "Quadrant ID Email change <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(parsed_new_email)
        .subject("Your Quadrant ID verification code")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template("Quadrant ID Email change request", &format!(
            "<h2>If you want to change your email, your verification code is: </h2> <h1>{}<h1><p>It is valid for 15 minutes. If you didn't want to change your email, you can ignore this email.</p>",
            verification_code_new
        ), DEFAULT_QUADRANT_ICON));

    if new_email_request.is_err() {
        return HttpResponse::InternalServerError().body("Failed to send verification email");
    }
    let new_email_request = new_email_request.unwrap();

    // Setup SMTP credentials and mailer
    let creds = Credentials::new(data.smtp.username.clone(), data.smtp.password.clone());
    let mailer = build_mailer(creds, data.smtp.url.clone());

    // Spawn cleanup task
    let data_clone = data.clone();
    let old_mail_clone = old_mail.clone();
    tokio::task::spawn(async move {
        tokio::time::sleep(tokio::time::Duration::from_secs(900)).await;

        let delete_result = QuadrantEmailUpdates::delete_many()
            .filter(quadrant_email_updates::Column::OldEmail.eq(old_mail_clone))
            .exec(&data_clone.conn)
            .await;

        if let Err(e) = delete_result {
            error!("Failed to delete expired verification code: {}", e);
        }
    });

    // Send verification emails
    match mailer.send(&old_email_request) {
        Ok(_) => match mailer.send(&new_email_request) {
            Ok(_) => HttpResponse::Accepted().body("Request created!"),
            Err(e) => HttpResponse::InternalServerError()
                .body(format!("Failed to send the email! ({})", e)),
        },
        Err(e) => {
            HttpResponse::InternalServerError().body(format!("Failed to send the email! ({})", e))
        }
    }
}

#[patch("/api/v3/account/data/email/update/request")]
pub async fn update_email_request_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    update_email_request_backend(_req, req_body, data).await
}

async fn update_email_backend(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> HttpResponse {
    // Parse request body
    let parse_res = serde_json::from_str::<UpdateConfirmation>(&req_body);
    if parse_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse your request");
    }
    let upd_request = parse_res.unwrap();

    let old_mail = upd_request.old_email.clone();
    let new_mail = upd_request.new_email.clone();

    // Validate email addresses
    let parsed_old_email_res = old_mail.parse::<Address>();
    if parsed_old_email_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse email");
    }
    let parsed_old_email = lettre::message::Mailbox::new(None, parsed_old_email_res.unwrap());

    let parsed_new_email_res = new_mail.parse::<Address>();
    if parsed_new_email_res.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse email");
    }
    let parsed_new_email = lettre::message::Mailbox::new(None, parsed_new_email_res.unwrap());

    // Verify email update request
    let update_request = QuadrantEmailUpdates::find()
        .filter(quadrant_email_updates::Column::OldEmail.eq(upd_request.old_email.clone()))
        .filter(quadrant_email_updates::Column::NewEmail.eq(upd_request.new_email.clone()))
        .filter(quadrant_email_updates::Column::OldCode.eq(upd_request.old_code))
        .filter(quadrant_email_updates::Column::NewCode.eq(upd_request.new_code))
        .one(&data.conn)
        .await;

    if update_request.is_err() || update_request.unwrap().is_none() {
        return HttpResponse::BadRequest().body("Invalid data was provided!");
    }

    // Update account email
    let update_result = QuadrantAccounts::update_many()
        .filter(quadrant_accounts::Column::Email.eq(upd_request.old_email.clone()))
        .set(quadrant_accounts::ActiveModel {
            email: Set(upd_request.new_email.clone()),
            ..Default::default()
        })
        .exec(&data.conn)
        .await;

    if let Err(e) = update_result {
        return HttpResponse::InternalServerError()
            .body(format!("Failed to update account: {}", e));
    }

    // Prepare notification emails
    let old_email_request = Message::builder()
        .from(
            "Quadrant ID Email change <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(parsed_old_email)
        .subject("Your Quadrant ID email was changed.")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template(
            "Your Quadrant ID email was changed",
            &format!(
                "<h2>Hello, your Quadrant ID email address was changed to {}</h2>",
                new_mail.clone()
            ),
            DEFAULT_QUADRANT_ICON,
        ))
        .unwrap();

    let new_email_request = Message::builder()
        .from(
            "Quadrant ID Email change <<EMAIL>>"
                .parse()
                .unwrap(),
        )
        .to(parsed_new_email)
        .subject("Your Quadrant ID email address was changed.")
        .header(ContentType::TEXT_HTML)
        .body(generate_email_template(
            "Your Quadrant ID email address was changed",
            "<h2>This is your new Quadrant ID email address!</h2>",
            DEFAULT_QUADRANT_ICON,
        ))
        .unwrap();

    // Setup SMTP credentials and mailer
    let creds = Credentials::new(data.smtp.username.clone(), data.smtp.password.clone());
    let mailer = build_mailer(creds, data.smtp.url.clone());

    // Delete the verification request
    let delete_result = QuadrantEmailUpdates::delete_many()
        .filter(quadrant_email_updates::Column::OldEmail.eq(old_mail))
        .exec(&data.conn)
        .await;

    if let Err(e) = delete_result {
        error!("Failed to delete verification code: {}", e);
    }

    // Send confirmation emails
    match mailer.send(&old_email_request) {
        Ok(_) => match mailer.send(&new_email_request) {
            Ok(_) => HttpResponse::Ok().body("Email updated!"),
            Err(e) => HttpResponse::InternalServerError()
                .body(format!("Failed to send the email! ({})", e)),
        },
        Err(e) => {
            HttpResponse::InternalServerError().body(format!("Failed to send the email! ({})", e))
        }
    }
}

#[patch("/api/v3/account/data/email/update")]
pub async fn update_email_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    update_email_backend(_req, req_body, data).await
}
