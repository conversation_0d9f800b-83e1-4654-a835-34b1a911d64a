use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, IntoActiveModel, QueryFilter, Set};
use serde::{Deserialize, Serialize};

use crate::util::entities::{quadrant_accounts, quadrant_notifications};
use qntapi::{WebData, verify_jwt};

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct Options {
    pub notification_id: String,
}

/// Handles the backend logic for marking notifications as read
///
/// # Arguments
/// * `_req` - The HTTP request containing the JWT token
/// * `data` - Web data containing database connection
/// * `options` - The notification options containing the notification ID
async fn read_notifications_backend(
    _req: HttpRequest,
    data: web::Data<WebData>,
    options: Options,
) -> HttpResponse {
    // Verify JWT token
    let token_res = verify_jwt(_req);
    if let Err(e) = token_res {
        return HttpResponse::Unauthorized().body(e.to_string());
    }
    let claims = token_res.unwrap();

    // Verify token has correct permissions
    if !claims.sub.contains("notifications") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    let uid = claims.uid;

    // Find the account using session_generation
    let account = quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(uid.to_string()))
        .one(&data.conn)
        .await;

    let account = match account {
        Ok(Some(account)) => account,
        Ok(None) => {
            return HttpResponse::BadRequest()
                .body("Account existence check fail 1: Account does not exist!");
        }
        Err(e) => {
            return HttpResponse::BadRequest()
                .body(format!("Account existence check fail 1: {}", e));
        }
    };

    // Find the notification
    let notification = quadrant_notifications::Entity::find()
        .filter(quadrant_notifications::Column::UserId.eq(account.id.clone()))
        .filter(quadrant_notifications::Column::NotificationId.eq(&options.notification_id))
        .one(&data.conn)
        .await;

    match notification {
        Ok(Some(notification)) => {
            let mut notification = notification.into_active_model();

            notification.read = Set(true);

            // Update the notification to mark it as read
            let res = notification.update(&data.conn).await;

            match res {
                Ok(_) => HttpResponse::Ok().body("READ."),
                Err(e) => {
                    HttpResponse::BadRequest().body(format!("Failed to update notification: {}", e))
                }
            }
        }
        Ok(None) => HttpResponse::BadRequest()
            .body("Account existence check fail 2: Notification does not exist!"),
        Err(e) => HttpResponse::BadRequest().body(format!("Account existence check fail 2: {}", e)),
    }
}

/// API endpoint for marking notifications as read
///
/// # Route
/// POST /api/v3/account/notifications/read
#[post("/api/v3/account/notifications/read")]
pub async fn read_notifications(
    _req: HttpRequest,
    data: web::Data<WebData>,
    options: web::Json<Options>,
) -> impl Responder {
    read_notifications_backend(_req, data, options.clone()).await
}
