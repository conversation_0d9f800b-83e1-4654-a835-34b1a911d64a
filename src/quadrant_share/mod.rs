use rand::Rng;
use serde::{Deserialize, Serialize};

use crate::util::entities::quadrant_share;

pub mod get;
pub mod share;
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]

pub struct QuadrantSharePublish {
    pub mod_config: String,
    pub uses_left: Option<i32>,
    pub hardware_id: String,
}
#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]

pub struct QuadrantShareGet {
    pub code: i32,
    pub mod_config: String,
    pub uses_left: i64,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]

pub struct QuadrantSharePublished {
    pub code: i32,
    pub uses_left: i64,
}

impl From<QuadrantSharePublish> for quadrant_share::Model {
    fn from(value: QuadrantSharePublish) -> Self {
        let mut rng = rand::rng();
        let code = rng.random_range(1000000..9999999);
        quadrant_share::Model {
            code,
            mod_config: value.mod_config,
            uses_left: value.uses_left.unwrap_or(5) as i64,
            hardware_id: value.hardware_id,
        }
    }
}
impl From<quadrant_share::Model> for QuadrantShareGet {
    fn from(value: quadrant_share::Model) -> Self {
        QuadrantShareGet {
            code: value.code,
            mod_config: value.mod_config,
            uses_left: value.uses_left,
        }
    }
}
