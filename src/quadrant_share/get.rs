use actix_web::{HttpRequest, HttpResponse, Responder, get, web};
use qstring::QString;
use sea_orm::{ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter};

use crate::util::entities::quadrant_share::{self, ActiveModel as QuadrantShareActive};
use qntapi::WebData;

use super::QuadrantShareGet;

/// Handles the backend logic for retrieving and updating shared modpack data
async fn quadrant_share_get_backend(
    _req: HttpRequest,
    webdata: web::Data<WebData>,
) -> HttpResponse {
    let qs = QString::from(_req.query_string());
    let modpack_code: Option<&str> = qs.get("code");

    if modpack_code.is_none() {
        return HttpResponse::BadRequest().body("No code specified");
    }

    let modpack_code = modpack_code.unwrap();

    let modpack_code = modpack_code.parse::<i32>();

    if modpack_code.is_err() {
        return HttpResponse::BadRequest().body("No valid code specified");
    }

    let modpack_code = modpack_code.unwrap();

    match handle_share_operations(&webdata.conn, modpack_code).await {
        Ok(share) => match serde_json::to_string_pretty(&share) {
            Ok(json) => HttpResponse::Ok().body(json),
            Err(_) => HttpResponse::InternalServerError().body("Failed to get your shared modpack"),
        },
        Err(_) => HttpResponse::InternalServerError().body("Failed to get your shared modpack"),
    }
}

/// Handles the database operations for the shared modpack
async fn handle_share_operations(
    db: &DatabaseConnection,
    code: i32,
) -> Result<QuadrantShareGet, Box<dyn std::error::Error>> {
    // Find the shared modpack
    let share = quadrant_share::Entity::find()
        .filter(quadrant_share::Column::Code.eq(code))
        .one(db)
        .await?;

    let mut share = share.ok_or("Share not found")?;

    // Update the uses_left count
    share.uses_left -= 1;

    // Convert to QuadrantShareGet to exclude hardware_id
    let response = QuadrantShareGet {
        code: share.code,
        mod_config: share.mod_config.clone(),
        uses_left: share.uses_left,
    };

    if share.uses_left == 0 {
        // Delete the share if no uses left
        quadrant_share::Entity::delete_by_id(code).exec(db).await?;
    } else {
        // Update the remaining uses
        let mut active_model: QuadrantShareActive = share.into();
        active_model.uses_left = sea_orm::Set(response.uses_left);
        active_model.update(db).await?;
    }

    Ok(response)
}

/// API endpoint for getting shared modpack data (v3)
#[get("/api/v3/quadrant/share/get")]
pub async fn quadrant_share_get_v3(
    _req: HttpRequest,
    webdata: web::Data<WebData>,
) -> impl Responder {
    quadrant_share_get_backend(_req, webdata).await
}
