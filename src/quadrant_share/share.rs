use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use log::info;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, IntoActiveModel, QueryFilter};

use crate::util::entities::{quadrant_accounts, quadrant_share, quadrant_usage};
use qntapi::{ApiKeySearchFilter, WebData, get_api_keys, verify_jwt};

use super::{QuadrantSharePublish, QuadrantSharePublished};

/// Handles the core share submission logic
async fn quadrant_share_submit_backend(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
    share_limit: i64,
) -> HttpResponse {
    let quadrant_share_raw = serde_json::from_str::<QuadrantSharePublish>(&req_body);
    if quadrant_share_raw.is_err() {
        return HttpResponse::BadRequest().body("Failed to parse the body");
    }
    let q_share_publish = quadrant_share_raw.unwrap();

    // Verify hardware ID exists in usage table
    let hwid = q_share_publish.clone().hardware_id;
    let usage_exists = quadrant_usage::Entity::find()
        .filter(quadrant_usage::Column::HardwareId.eq(hwid.clone()))
        .one(&webdata.conn)
        .await;

    match usage_exists {
        Ok(Some(_)) => (),
        _ => return HttpResponse::InternalServerError().body("Failed to share your modpack"),
    }

    // Delete existing shares for this hardware ID
    let delete_result = quadrant_share::Entity::delete_many()
        .filter(quadrant_share::Column::HardwareId.eq(hwid.clone()))
        .exec(&webdata.conn)
        .await;

    if delete_result.is_err() {
        return HttpResponse::InternalServerError().body("Failed to share your modpack");
    }

    // Create new share
    let mut q_share: quadrant_share::Model = q_share_publish.into();
    q_share.uses_left = share_limit;

    // Convert to SeaORM active model and insert
    let share_model = q_share.into_active_model();

    match share_model.insert(&webdata.conn).await {
        Ok(inserted) => {
            let response = QuadrantSharePublished {
                code: inserted.code,
                uses_left: inserted.uses_left,
            };
            match serde_json::to_string_pretty(&response) {
                Ok(json) => HttpResponse::Created().body(json),
                Err(_) => {
                    HttpResponse::InternalServerError().body("Failed to get the sharing code")
                }
            }
        }
        Err(_) => HttpResponse::InternalServerError().body("Failed to share your modpack"),
    }
}

/// Handles share submission without ID verification
async fn quadrant_share_submit_backend_no_id(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
) -> HttpResponse {
    let api_key_filter = ApiKeySearchFilter {
        product: "quadrant".to_string(),
        scope: "quadrant_share".to_string(),
    };
    let get_keys_res = get_api_keys(webdata.as_ref().to_owned(), api_key_filter).await;
    if get_keys_res.is_err() {
        return HttpResponse::InternalServerError()
            .body("Failed to verify your API key (doesn't mean that it's invalid)");
    }
    let authentication_keys: Vec<String> = get_keys_res.unwrap();
    if _req.headers().get("Authorization").is_none()
        || !authentication_keys.contains(
            &_req
                .headers()
                .get("Authorization")
                .unwrap()
                .to_str()
                .unwrap()
                .to_owned(),
        )
    {
        info!(
            "{}",
            ("Invalid API Key ".to_string()
                + _req
                    .headers()
                    .get("Authorization")
                    .unwrap()
                    .to_str()
                    .unwrap()
                    .trim())
        );
        return HttpResponse::Forbidden().body("Your API key is invalid.");
    }
    quadrant_share_submit_backend(_req, req_body, webdata, 1).await
}

/// Handles share submission with ID verification
async fn quadrant_share_submit_backend_id(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
) -> HttpResponse {
    let claims = verify_jwt(_req.clone());

    if claims.is_err() {
        return HttpResponse::Unauthorized().body(claims.err().unwrap().to_string());
    }
    let claims = claims.unwrap();

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Find account and get share limit
    let account = quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&webdata.conn)
        .await;

    let account = match account {
        Ok(Some(acc)) => acc,
        _ => return HttpResponse::InternalServerError().body("Failed to get the account"),
    };

    let extra_info = qntapi::get_share_sync_limit_and_groups(account.id, &webdata.conn).await;
    let share_limit = extra_info.1;

    quadrant_share_submit_backend(_req, req_body, webdata, share_limit).await
}

#[post("/api/v3/quadrant/share/submit/id")]
pub async fn quadrant_share_submit_id_v3(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
) -> impl Responder {
    quadrant_share_submit_backend_id(_req, req_body, webdata).await
}

#[post("/api/v3/quadrant/share/submit")]
pub async fn quadrant_share_submit_v3(
    _req: HttpRequest,
    req_body: String,
    webdata: web::Data<WebData>,
) -> impl Responder {
    quadrant_share_submit_backend_no_id(_req, req_body, webdata).await
}
