use actix_cors::Cors;
use actix_web::middleware::Logger;
use actix_web::{App, HttpResponse, HttpServer, Responder, get, web};
use chrono::Duration;
use env_logger::Env;

mod other;
mod quadrant_id;
mod quadrant_settings_sync;
mod quadrant_share;
mod quadrant_subscription;
mod quadrant_sync;
mod quadrant_usage;
mod tauri_updates;
mod util;

use lemonsqueezy::LemonSqueezy;
use qntapi::{LemonSqueezyData, OAuth2App, SMTPCredentials, WebData};
use sea_orm::{ConnectOptions, Database};

const VERSION: &str = env!("CARGO_PKG_VERSION");

#[get("/")]
async fn root() -> impl Responder {
    HttpResponse::Ok().body(format!("MrQuantumOFF.DEV API (v{})", VERSION))
}

#[tokio::main]
async fn main() -> std::io::Result<()> {
    dotenvy::dotenv().ok();

    // let config = load_rustls_config();
    let db_url = std::env::var("DATABASE_URL").expect("Please define DATABASE_URL");

    let mut opt = ConnectOptions::new(db_url);
    opt.max_connections(100)
        .min_connections(5)
        .connect_timeout(Duration::seconds(10).to_std().unwrap())
        .acquire_timeout(Duration::seconds(8).to_std().unwrap())
        .idle_timeout(Duration::seconds(300).to_std().unwrap())
        .max_lifetime(Duration::minutes(15).to_std().unwrap())
        .sqlx_logging(false)
        .sqlx_logging_level(log::LevelFilter::Info)
        .set_schema_search_path("qntdb"); // Setting default PostgreSQL schema
    let conn = Database::connect(opt).await.unwrap();
    let discord_verify_client_id: String =
        std::env::var("DS_VERIFY_CLIENT_ID").unwrap_or("0".to_string());
    let discord_verify_client_secret: String =
        std::env::var("DS_VERIFY_CLIENT_SECRET").unwrap_or("".to_string());
    let discord_verify_redirect_uri: String =
        std::env::var("DS_VERIFY_REDIRECT_URI").unwrap_or("".to_string());

    let lemon_squeezy_webhook_secret =
        std::env::var("LEMON_SQUEEZY_WEBHOOK_SECRET").unwrap_or("".to_string());
    let lemon_squeezy_api_key = std::env::var("LEMON_SQUEEZY_API_SECRET").unwrap_or("".to_string());
    let plus_sub_id = std::env::var("PLUS_SUB_ID")
        .unwrap_or("0".to_string())
        .parse::<i64>()
        .expect("Failed to parse plus sub id");
    let lemon_store_id = std::env::var("LEMON_STORE_ID")
        .unwrap_or("0".to_string())
        .parse::<i64>()
        .expect("Failed to parse lemon store id");
    let plus_sub_group_id = std::env::var("PLUS_SUB_GROUP_ID").unwrap_or("".to_string());

    let data = WebData {
        discord_verify_oauth2: OAuth2App {
            client_id: discord_verify_client_id.clone(),
            client_secret: discord_verify_client_secret.clone(),
            redirect_uri: discord_verify_redirect_uri.clone(),
        },

        smtp: SMTPCredentials {
            username: std::env::var("SMTP_USERNAME").unwrap_or("".to_string()),
            password: std::env::var("SMTP_PASSWORD").unwrap_or("".to_string()),
            url: std::env::var("SMTP_URL").unwrap_or("".to_string()),
        },

        conn,
        lemon: LemonSqueezyData {
            webhook_secret: lemon_squeezy_webhook_secret,
            plus_sub_id,
            store_id: lemon_store_id,
            plus_sub_gid: plus_sub_group_id,
            ls_instance: LemonSqueezy::new(lemon_squeezy_api_key.clone()),
            api_key: lemon_squeezy_api_key,
        },
    };
    env_logger::init_from_env(Env::default().default_filter_or("INFO"));
    HttpServer::new(move || {
        let cors = Cors::default()
            .allowed_origin("https://mrquantumoff.dev")
            .allowed_origin("https://api.mrquantumoff.dev")
            .allowed_origin("https://localhost")
            .allowed_origin("http://localhost")
            .allowed_origin("http://localhost:3000")
            .allowed_origin_fn(|origin, _req_head| {
                origin.as_bytes().ends_with(b".mrquantumoff.dev")
            })
            .allowed_origin_fn(|origin, _req_head| origin.as_bytes().ends_with(b".vercel.app"))
            .allow_any_header()
            .allow_any_method()
            .expose_any_header()
            .send_wildcard()
            .max_age(7200);
        App::new()
            .wrap(Logger::default())
            .wrap(cors)
            .app_data(web::Data::new(data.clone()))
            .service(quadrant_id::auth::register::register_request_v3)
            .service(quadrant_id::auth::register::register_v3)
            .service(quadrant_id::delete_account::delete_account_v3)
            .service(quadrant_id::update::update_account::update_data_v3)
            .service(quadrant_id::get_user_data::get_info_v3)
            .service(quadrant_id::auth::sign_in::login_v3)
            .service(quadrant_id::update::update_email::update_email_request_v3)
            .service(quadrant_id::update::update_email::update_email_v3)
            .service(quadrant_id::auth::reset_password::request_password_reset_v3)
            .service(quadrant_id::auth::reset_password::reset_password_v3)
            .service(quadrant_id::auth::oauth2::oauth2_request_v3)
            .service(quadrant_id::auth::oauth2::oauth2_sign_in_v3)
            .service(quadrant_id::auth::otp::otp_setup_v3)
            .service(quadrant_id::auth::otp::otp_verification_v3)
            .service(quadrant_id::auth::otp::otp_disable_v3)
            .service(quadrant_id::read_notifications::read_notifications)
            .service(tauri_updates::get_versions::get_versions)
            .service(tauri_updates::submit_versions::submit_new_version)
            .service(other::legacy_blog::get_rss)
            .service(other::verifydiscorduser::logindiscorduser)
            .service(other::verifydiscorduser::verifydiscorduser)
            .service(quadrant_settings_sync::submit_sync::quadrant_settings_sync_submit_v3)
            .service(quadrant_settings_sync::get_sync::quadrant_settings_sync_get_v3)
            .service(quadrant_share::get::quadrant_share_get_v3)
            .service(quadrant_share::share::quadrant_share_submit_id_v3)
            .service(quadrant_share::share::quadrant_share_submit_v3)
            .service(quadrant_sync::delete_sync::quadrant_sync_delete_v3)
            .service(quadrant_sync::submit_sync::quadrant_sync_submit_v3)
            .service(quadrant_sync::get_sync::quadrant_sync_get_v3)
            .service(quadrant_sync::invite_to_sync::invite_to_sync)
            .service(quadrant_sync::invite_to_sync::respond_to_sync)
            .service(quadrant_sync::kick_member::quadrant_sync_member_kick)
            .service(quadrant_usage::getusageinfo::get_usage_info_v3)
            .service(quadrant_usage::submitusageinfo::submit_usage_info_v3)
            .service(quadrant_usage::deleteusageinfo::delete_usage_info_v3)
            .service(quadrant_subscription::handle_subscriptions)
            .service(root)
    })
    .bind("0.0.0.0:6000")?
    .run()
    .await
}
