//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_accounts")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: String,
    #[sea_orm(unique)]
    pub session_generation: String,
    #[sea_orm(unique)]
    pub username: String,
    pub display_name: String,
    #[sea_orm(column_type = "Text")]
    pub pass: String,
    #[sea_orm(unique)]
    pub email: String,
    pub otp_enabled: bool,
    pub email_notifications: bool,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::account_user_groups::Entity")]
    AccountUserGroups,
    #[sea_orm(has_many = "super::quadrant_notifications::Entity")]
    QuadrantNotifications,
    #[sea_orm(has_many = "super::quadrant_oauth2_requests::Entity")]
    QuadrantOauth2Requests,
    #[sea_orm(has_one = "super::quadrant_otp::Entity")]
    QuadrantOtp,
    #[sea_orm(has_one = "super::quadrant_settings_sync::Entity")]
    QuadrantSettingsSync,
    #[sea_orm(has_many = "super::quadrant_sync_owners::Entity")]
    QuadrantSyncOwners,
    #[sea_orm(has_many = "super::user_subscription::Entity")]
    UserSubscription,
}

impl Related<super::account_user_groups::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::AccountUserGroups.def()
    }
}

impl Related<super::quadrant_notifications::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantNotifications.def()
    }
}

impl Related<super::quadrant_oauth2_requests::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantOauth2Requests.def()
    }
}

impl Related<super::quadrant_otp::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantOtp.def()
    }
}

impl Related<super::quadrant_settings_sync::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantSettingsSync.def()
    }
}

impl Related<super::quadrant_sync_owners::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantSyncOwners.def()
    }
}

impl Related<super::user_subscription::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserSubscription.def()
    }
}

impl Related<super::user_groups::Entity> for Entity {
    fn to() -> RelationDef {
        super::account_user_groups::Relation::UserGroups.def()
    }
    fn via() -> Option<RelationDef> {
        Some(
            super::account_user_groups::Relation::QuadrantAccounts
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}
