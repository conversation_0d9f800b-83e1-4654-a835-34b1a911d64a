//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "tauri_updates")]
pub struct Model {
    pub product: String,
    pub version: String,
    pub pub_date: DateTimeWithTimeZone,
    pub arch: String,
    pub signature: String,
    pub platform: String,
    pub url: String,
    pub branch: String,
    pub public: bool,
    #[sea_orm(primary_key, auto_increment = false)]
    pub version_id: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
