//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_share")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub code: i32,
    #[sea_orm(column_type = "Text")]
    pub mod_config: String,
    pub uses_left: i64,
    #[sea_orm(unique)]
    pub hardware_id: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
