//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_notifications")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub notification_id: String,
    pub user_id: String,
    #[sea_orm(column_type = "Text")]
    pub message: String,
    pub created_at: DateTimeWithTimeZone,
    pub read: bool,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::quadrant_accounts::Entity",
        from = "Column::UserId",
        to = "super::quadrant_accounts::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    QuadrantAccounts,
}

impl Related<super::quadrant_accounts::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantAccounts.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
