//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "user_groups")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub group_id: String,
    pub group_name: String,
    pub privileges: Vec<String>,
    pub priority: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::account_user_groups::Entity")]
    AccountUserGroups,
}

impl Related<super::account_user_groups::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::AccountUserGroups.def()
    }
}

impl Related<super::quadrant_accounts::Entity> for Entity {
    fn to() -> RelationDef {
        super::account_user_groups::Relation::QuadrantAccounts.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::account_user_groups::Relation::UserGroups.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}
