//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "uabot_member_approval_servers")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub guild_id: i64,
    pub channel_id: i64,
    pub role_id: i64,
    pub post_verify: bool,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::uabot_verified_servers::Entity",
        from = "Column::GuildId",
        to = "super::uabot_verified_servers::Column::GuildId",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    UabotVerifiedServers,
}

impl Related<super::uabot_verified_servers::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UabotVerifiedServers.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
