//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "account_user_groups")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub account_id: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub group_id: String,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::quadrant_accounts::Entity",
        from = "Column::AccountId",
        to = "super::quadrant_accounts::Column::Id",
        on_update = "NoAction",
        on_delete = "Cascade"
    )]
    QuadrantAccounts,
    #[sea_orm(
        belongs_to = "super::user_groups::Entity",
        from = "Column::GroupId",
        to = "super::user_groups::Column::GroupId",
        on_update = "NoAction",
        on_delete = "Cascade"
    )]
    UserGroups,
}

impl Related<super::quadrant_accounts::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantAccounts.def()
    }
}

impl Related<super::user_groups::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserGroups.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
