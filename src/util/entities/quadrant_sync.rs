//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_sync")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub modpack_id: String,
    pub name: String,
    #[sea_orm(column_type = "Text")]
    pub minecraft_version: String,
    #[sea_orm(column_type = "Text")]
    pub mod_loader: String,
    #[sea_orm(column_type = "Text")]
    pub mods: String,
    pub last_synced: DateTimeWithTimeZone,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::quadrant_sync_owners::Entity")]
    QuadrantSyncOwners,
}

impl Related<super::quadrant_sync_owners::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantSyncOwners.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
