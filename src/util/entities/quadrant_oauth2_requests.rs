//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_oauth2_requests")]
pub struct Model {
    pub client_id: String,
    pub client_secret: String,
    pub user_id: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub code: String,
    pub token: String,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::quadrant_accounts::Entity",
        from = "Column::UserId",
        to = "super::quadrant_accounts::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    QuadrantAccounts,
}

impl Related<super::quadrant_accounts::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantAccounts.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
