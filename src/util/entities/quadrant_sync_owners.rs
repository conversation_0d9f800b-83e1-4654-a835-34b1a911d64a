//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_sync_owners")]
pub struct Model {
    pub user_id: String,
    pub admin: bool,
    pub modpack_id: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub owner_id: String,
    pub accepted: bool,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::quadrant_accounts::Entity",
        from = "Column::UserId",
        to = "super::quadrant_accounts::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    QuadrantAccounts,
    #[sea_orm(
        belongs_to = "super::quadrant_sync::Entity",
        from = "Column::ModpackId",
        to = "super::quadrant_sync::Column::ModpackId",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    QuadrantSync,
}

impl Related<super::quadrant_accounts::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantAccounts.def()
    }
}

impl Related<super::quadrant_sync::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::QuadrantSync.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
