//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_usage")]
pub struct Model {
    #[sea_orm(column_type = "Text")]
    pub version: String,
    #[sea_orm(column_type = "Text")]
    pub os: String,
    pub modrinth_usage: i64,
    pub curseforge_usage: i64,
    pub reference_file_usage: i64,
    pub manual_input_usage: i64,
    #[sea_orm(unique)]
    pub hardware_id: String,
    #[sea_orm(column_type = "Text")]
    pub country: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub entry_timestamp: DateTimeWithTimeZone,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Enum<PERSON><PERSON>, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
