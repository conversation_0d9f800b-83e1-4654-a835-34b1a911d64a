//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(
    schema_name = "qntdb",
    table_name = "quadrant_id_reset_password_requests"
)]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub account_id: String,
    #[sea_orm(unique)]
    pub email: String,
    pub code: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::quadrant_accounts::Entity",
        from = "Column::Email",
        to = "super::quadrant_accounts::Column::Email",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    QuadrantAccounts2,
    #[sea_orm(
        belongs_to = "super::quadrant_accounts::Entity",
        from = "Column::AccountId",
        to = "super::quadrant_accounts::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    QuadrantAccounts1,
}

impl ActiveModelBehavior for ActiveModel {}
