//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "uabot_verified_servers")]
pub struct Model {
    pub channel_message_id: i64,
    #[sea_orm(primary_key, auto_increment = false)]
    pub guild_id: i64,
    pub role_id: i64,
    pub remove_messages: bool,
    pub easy_verify: bool,
    pub member_approval: bool,
}

#[derive(<PERSON><PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_one = "super::uabot_member_approval_servers::Entity")]
    UabotMemberApprovalServers,
}

impl Related<super::uabot_member_approval_servers::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UabotMemberApprovalServers.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
