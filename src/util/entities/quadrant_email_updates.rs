//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "quadrant_email_updates")]
pub struct Model {
    pub new_code: i32,
    #[sea_orm(unique)]
    pub old_code: i32,
    #[sea_orm(unique)]
    pub old_email: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub new_email: String,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
