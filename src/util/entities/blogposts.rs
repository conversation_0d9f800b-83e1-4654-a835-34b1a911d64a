//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.6

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "qntdb", table_name = "blogposts")]
pub struct Model {
    #[sea_orm(column_type = "Text")]
    pub title: String,
    #[sea_orm(column_type = "Text")]
    pub link: String,
    #[sea_orm(column_type = "Text")]
    pub description: String,
    #[sea_orm(column_type = "Text")]
    pub author: String,
    #[sea_orm(column_type = "Text")]
    pub categories: String,
    #[sea_orm(column_type = "Text")]
    pub enclosure: String,
    pub enclosure_enabled: bool,
    pub enclosure_length: i64,
    #[sea_orm(column_type = "Text")]
    pub enclosure_mime_type: String,
    #[sea_orm(column_type = "Text")]
    pub date: String,
    #[sea_orm(column_type = "Text")]
    pub source: String,
    #[sea_orm(column_type = "Text")]
    pub content: String,
    #[sea_orm(column_type = "Text")]
    pub guid: String,
    #[sea_orm(primary_key, auto_increment = false)]
    pub entry_timestamp: i64,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
