use actix_web::{HttpRequest, HttpResponse, Responder, get, web};
use sea_orm::*;
use serde::{Deserialize, Serialize};

use crate::util::entities::{quadrant_accounts, quadrant_sync, quadrant_sync_owners};
use qntapi::{WebData, verify_jwt};

use crate::quadrant_sync::shared::{OwnerPublic, QuadrantSyncResponse};

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ExtraGetOptions {
    modpack_id: Option<String>,
    show_owners: Option<bool>,
}

/// Get synchronized modpacks for the authenticated user
///
/// Optionally filters by modpack_id and can include owner information
#[get("/api/v3/quadrant/sync/get")]
pub async fn quadrant_sync_get_v3(
    _req: HttpRequest,
    data: web::Data<WebData>,
    options: web::Query<ExtraGetOptions>,
) -> impl Responder {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Find account by session generation
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::Unauthorized().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Build query for sync owners
    let mut sync_owners_query = quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&account.id))
        .filter(quadrant_sync_owners::Column::Accepted.eq(true));

    // Add modpack_id filter if specified
    if let Some(ref modpack_id) = options.modpack_id {
        sync_owners_query =
            sync_owners_query.filter(quadrant_sync_owners::Column::ModpackId.eq(modpack_id));
    }

    // Get owned modpacks
    let sync_owners = match sync_owners_query.all(&data.conn).await {
        Ok(owners) => owners,
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    let mut modpacks = Vec::new();

    // Process each owned modpack
    for owned_modpack in sync_owners {
        // Get modpack details
        let modpack = match quadrant_sync::Entity::find()
            .filter(quadrant_sync::Column::ModpackId.eq(&owned_modpack.modpack_id))
            .one(&data.conn)
            .await
        {
            Ok(Some(modpack)) => modpack,
            Ok(None) => continue, // Skip if modpack not found
            Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
        };

        let mut owners = Vec::new();
        let mut modpack = QuadrantSyncResponse::new(modpack);

        // Get owners list if requested
        if options.show_owners == Some(true) {
            let owners_list = match quadrant_sync_owners::Entity::find()
                .filter(quadrant_sync_owners::Column::ModpackId.eq(&modpack.modpack_id))
                .all(&data.conn)
                .await
            {
                Ok(list) => list,
                Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
            };

            // Get account details for each owner
            for owner in owners_list {
                let owner_account = match quadrant_accounts::Entity::find()
                    .filter(quadrant_accounts::Column::Id.eq(owner.user_id))
                    .one(&data.conn)
                    .await
                {
                    Ok(Some(account)) => account,
                    Ok(None) => continue, // Skip if account not found
                    Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
                };

                owners.push(OwnerPublic {
                    username: owner_account.username,
                    admin: owner.admin,
                });
            }
        }

        modpack.owners = owners;

        // Build response object
        modpacks.push(modpack);
    }

    HttpResponse::Ok().json(modpacks)
}
