use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use chrono::{DateTime, Utc};
use sea_orm::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::util::entities::{quadrant_accounts, quadrant_sync, quadrant_sync_owners};
use qntapi::{WebData, verify_jwt};

use crate::quadrant_sync::shared::QuadrantSyncResponse;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QuadrantSyncRequest {
    pub mod_loader: String,
    pub mc_version: String,
    pub mods: String,
    pub name: String,
    pub overwrite: Option<bool>,
    pub last_synced: i64,
}

impl From<QuadrantSyncRequest> for quadrant_sync::ActiveModel {
    fn from(value: QuadrantSyncRequest) -> Self {
        quadrant_sync::ActiveModel {
            name: Set(value.name),
            minecraft_version: Set(value.mc_version),
            mod_loader: Set(value.mod_loader),
            mods: Set(value.mods),
            modpack_id: Set(Uuid::now_v7().to_string()),
            last_synced: Set(DateTime::from_timestamp_millis(value.last_synced)
                .unwrap()
                .to_utc()
                .into()),
        }
    }
}

/// Submit or update a modpack sync
#[post("/api/v3/quadrant/sync/submit")]
pub async fn quadrant_sync_submit_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Parse request body
    let q_sync_request = match serde_json::from_str::<QuadrantSyncRequest>(&req_body) {
        Ok(req) => req,
        Err(_) => return HttpResponse::BadRequest().body("Failed to parse the body"),
    };

    // Get user's account
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::Unauthorized().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Get owned modpacks
    let owned_modpacks = quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&account.id))
        .filter(quadrant_sync_owners::Column::Accepted.eq(true))
        .all(&data.conn)
        .await
        .unwrap_or_default();

    // Get modpack details
    let mut modpacks = Vec::new();
    for owned_modpack in &owned_modpacks {
        if let Ok(Some(modpack)) = quadrant_sync::Entity::find()
            .filter(quadrant_sync::Column::ModpackId.eq(&owned_modpack.modpack_id))
            .one(&data.conn)
            .await
        {
            modpacks.push(modpack);
        }
    }

    let names: Vec<String> = modpacks.iter().map(|obj| obj.name.clone()).collect();

    // Check sync limits
    let (_, _, sync_limit) =
        qntapi::get_share_sync_limit_and_groups(account.id.clone(), &data.conn).await;

    if modpacks.len() as i64 >= sync_limit && !names.contains(&q_sync_request.name) {
        return HttpResponse::BadRequest().body("Sync limit reached");
    }

    let sync_date = Utc::now();
    let mut modpack: quadrant_sync::ActiveModel = q_sync_request.clone().into();
    modpack.last_synced = Set(sync_date.into());

    // Handle existing modpack update or new modpack creation
    if names.contains(&q_sync_request.name) {
        let index = names
            .iter()
            .position(|r| r == &q_sync_request.name)
            .unwrap();
        let old_modpack = &modpacks[index];

        if q_sync_request.last_synced < old_modpack.last_synced.timestamp_millis()
            && !q_sync_request.overwrite.unwrap_or(false)
        {
            return HttpResponse::BadRequest().body("Cloud sync is newer");
        }

        modpack.modpack_id = Set(old_modpack.modpack_id.clone());

        // Update existing modpack
        match quadrant_sync::Entity::update(modpack)
            .filter(quadrant_sync::Column::ModpackId.eq(&old_modpack.modpack_id))
            .exec(&data.conn)
            .await
        {
            Ok(updated_modpack) => {
                return HttpResponse::Ok().json(QuadrantSyncResponse::new(
                    updated_modpack.try_into_model().unwrap(),
                ));
            }
            Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
        }
    } else {
        // Create new modpack and ownership
        let txn = data.conn.begin().await.unwrap();

        // Insert new modpack
        let modpack_model = match modpack.insert(&txn).await {
            Ok(model) => model,
            Err(e) => {
                let _ = txn.rollback().await;
                return HttpResponse::InternalServerError().body(e.to_string());
            }
        };

        // Create ownership record
        let owner = quadrant_sync_owners::ActiveModel {
            user_id: Set(account.id),
            modpack_id: Set(modpack_model.modpack_id.clone()),
            admin: Set(true),
            owner_id: Set(Uuid::now_v7().to_string()),
            accepted: Set(true),
        };

        match owner.insert(&txn).await {
            Ok(_) => match txn.commit().await {
                Ok(_) => return HttpResponse::Ok().json(QuadrantSyncResponse::new(modpack_model)),
                Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
            },
            Err(e) => {
                let _ = txn.rollback().await;
                HttpResponse::InternalServerError().body(e.to_string())
            }
        }
    }
}
