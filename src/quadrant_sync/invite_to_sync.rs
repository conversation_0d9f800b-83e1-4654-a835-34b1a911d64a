use actix_web::{HttpRequest, HttpResponse, Responder, post, web};
use sea_orm::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::util::entities::{quadrant_accounts, quadrant_sync_owners};
use qntapi::{WebData, send_notification, verify_jwt};

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct InviteOptions {
    modpack_id: String,
    username: String,
    admin: bool,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ResponseOptions {
    modpack_id: String,
    accept: bool,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct InviteNotification {
    simple_message: String,
    message: String,
    notification_type: String,
    invite_id: String,
    admin: bool,
}

/// Invite a user to collaborate on a modpack
#[post("/api/v3/quadrant/sync/invite")]
pub async fn invite_to_sync(
    _req: HttpRequest,
    data: web::Data<WebData>,
    options: web::Json<InviteOptions>,
) -> impl Responder {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Get inviter's account
    let inviter_account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::Unauthorized().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Find target account by username
    let target_account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::Username.eq(&options.username))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::BadRequest().body("Account does not exist"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Check if inviter has admin rights
    let inviter_ownership = match quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&inviter_account.id))
        .filter(quadrant_sync_owners::Column::ModpackId.eq(&options.modpack_id))
        .one(&data.conn)
        .await
    {
        Ok(Some(ownership)) => ownership,
        Ok(None) => return HttpResponse::BadRequest().body("Modpack does not exist"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    if !inviter_ownership.admin {
        return HttpResponse::Forbidden().body("Insufficient permissions");
    }

    // Check if target is already invited
    match quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&target_account.id))
        .filter(quadrant_sync_owners::Column::ModpackId.eq(&options.modpack_id))
        .one(&data.conn)
        .await
    {
        Ok(Some(_)) => return HttpResponse::BadRequest().body("User already invited to modpack"),
        Ok(None) => {}
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Create new ownership record
    let owner_id = Uuid::now_v7().to_string();
    let new_owner = quadrant_sync_owners::ActiveModel {
        user_id: Set(target_account.id.clone()),
        modpack_id: Set(options.modpack_id.clone()),
        admin: Set(options.admin),
        accepted: Set(false),
        owner_id: Set(owner_id.clone()),
    };

    // Insert new ownership
    match new_owner.insert(&data.conn).await {
        Ok(_) => (),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Send notification to target user
    let notification = InviteNotification {
        admin: options.admin,
        simple_message: format!(
            "You have been invited to collaborate on a modpack by {}, open this notification in Quadrant v24.7 or higher in order to accept or decline this proposition.",
            inviter_account.display_name
        ),
        message: format!(
            "You have been invited to collaborate on a modpack by {}",
            inviter_account.display_name
        ),
        notification_type: "invite_to_sync".to_string(),
        invite_id: owner_id,
    };

    match send_notification(target_account.id, notification, data).await {
        Ok(res) => HttpResponse::Ok().json(res),
        Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
    }
}

/// Handle response to sync invitation
#[post("/api/v3/quadrant/sync/respond")]
pub async fn respond_to_sync(
    _req: HttpRequest,
    data: web::Data<WebData>,
    options: web::Json<ResponseOptions>,
) -> impl Responder {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Get responder's account
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::Unauthorized().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Find invitation
    let invitation = match quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&account.id))
        .filter(quadrant_sync_owners::Column::OwnerId.eq(&options.modpack_id))
        .filter(quadrant_sync_owners::Column::Accepted.eq(false))
        .one(&data.conn)
        .await
    {
        Ok(Some(invite)) => invite,
        Ok(None) => return HttpResponse::BadRequest().body("Modpack does not exist"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    if options.accept {
        // Accept invitation
        let mut invite_model: quadrant_sync_owners::ActiveModel = invitation.into();
        invite_model.accepted = Set(true);
        match invite_model.update(&data.conn).await {
            Ok(_) => HttpResponse::Ok().body("OK"),
            Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
        }
    } else {
        // Decline invitation - delete the record
        match quadrant_sync_owners::Entity::delete_by_id(invitation.owner_id)
            .exec(&data.conn)
            .await
        {
            Ok(_) => HttpResponse::Ok().body("OK"),
            Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
        }
    }
}
