use actix_web::{HttpRequest, HttpResponse, Responder, delete, web};
use sea_orm::*;
use serde::{Deserialize, Serialize};

use crate::util::entities::{quadrant_accounts, quadrant_sync, quadrant_sync_owners};
use qntapi::{WebData, verify_jwt};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QuadrantSyncDeletionRequest {
    pub modpack_id: String,
    pub name: Option<String>,
}

/// Handles deletion of synchronized modpacks
///
/// If the user is an admin, deletes the entire modpack.
/// If the user is not an admin, only removes their ownership.
#[delete("/api/v3/quadrant/sync/delete")]
pub async fn quadrant_sync_delete_v3(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    // Verify token subject
    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Parse request body
    let q_sync_request = match serde_json::from_str::<QuadrantSyncDeletionRequest>(&req_body) {
        Ok(req) => req,
        Err(_) => return HttpResponse::BadRequest().body("Failed to parse the body"),
    };

    // Find the account using session generation
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::Unauthorized().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Find sync owner entry
    let sync_owner = match quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&account.id))
        .filter(quadrant_sync_owners::Column::ModpackId.eq(&q_sync_request.modpack_id))
        .one(&data.conn)
        .await
    {
        Ok(Some(owner)) => owner,
        Ok(None) => return HttpResponse::BadRequest().body("Modpack does not exist"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Delete based on user's admin status
    let result = if !sync_owner.admin {
        // Non-admin: only delete ownership
        quadrant_sync_owners::Entity::delete_many()
            .filter(quadrant_sync_owners::Column::UserId.eq(&account.id))
            .filter(quadrant_sync_owners::Column::ModpackId.eq(&q_sync_request.modpack_id))
            .exec(&data.conn)
            .await
    } else {
        // Admin: delete entire modpack
        quadrant_sync::Entity::delete_many()
            .filter(quadrant_sync::Column::ModpackId.eq(&sync_owner.modpack_id))
            .exec(&data.conn)
            .await
    };

    match result {
        Ok(_) => HttpResponse::Ok().body("Deleted/Left."),
        Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
    }
}
