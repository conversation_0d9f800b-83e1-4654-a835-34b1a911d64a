use actix_web::{HttpRequest, HttpResponse, Responder, delete, web};
use sea_orm::*;
use serde::{Deserialize, Serialize};

use crate::util::entities::{quadrant_accounts, quadrant_sync_owners};
use qntapi::{WebData, verify_jwt};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QuadrantSyncMemberKickRequest {
    pub modpack_id: String,
    pub username: String,
}

/// Endpoint to kick a member from a synced modpack
#[delete("/api/v3/quadrant/sync/kick")]
pub async fn quadrant_sync_member_kick(
    _req: HttpRequest,
    req_body: String,
    data: web::Data<WebData>,
) -> impl Responder {
    // Verify JWT token
    let claims = match verify_jwt(_req) {
        Ok(claims) => claims,
        Err(e) => return HttpResponse::Unauthorized().body(e.to_string()),
    };

    if !claims.sub.contains("quadrant_sync") {
        return HttpResponse::Forbidden().body("Invalid token subject");
    }

    // Parse request body
    let q_sync_request = match serde_json::from_str::<QuadrantSyncMemberKickRequest>(&req_body) {
        Ok(req) => req,
        Err(_) => return HttpResponse::BadRequest().body("Failed to parse the body"),
    };

    // Get kicker's account
    let account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::SessionGeneration.eq(claims.uid))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::Unauthorized().body("Account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Get target account
    let target_account = match quadrant_accounts::Entity::find()
        .filter(quadrant_accounts::Column::Username.eq(&q_sync_request.username))
        .one(&data.conn)
        .await
    {
        Ok(Some(account)) => account,
        Ok(None) => return HttpResponse::BadRequest().body("Target account not found"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Check if kicker has admin rights
    let kicker_ownership = match quadrant_sync_owners::Entity::find()
        .filter(quadrant_sync_owners::Column::UserId.eq(&account.id))
        .filter(quadrant_sync_owners::Column::ModpackId.eq(&q_sync_request.modpack_id))
        .one(&data.conn)
        .await
    {
        Ok(Some(ownership)) => ownership,
        Ok(None) => return HttpResponse::BadRequest().body("Modpack does not exist"),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    if !kicker_ownership.admin {
        return HttpResponse::Forbidden().body("You don't have the right to kick people.");
    }

    // Delete target's ownership record
    match quadrant_sync_owners::Entity::delete_many()
        .filter(quadrant_sync_owners::Column::UserId.eq(target_account.id))
        .filter(quadrant_sync_owners::Column::ModpackId.eq(q_sync_request.modpack_id))
        .exec(&data.conn)
        .await
    {
        Ok(_) => HttpResponse::Ok().body("Kicked."),
        Err(e) => HttpResponse::InternalServerError().body(e.to_string()),
    }
}
