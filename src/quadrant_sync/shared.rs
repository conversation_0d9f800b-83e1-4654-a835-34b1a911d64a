use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>ialize, Deserialize, <PERSON><PERSON>)]
pub struct QuadrantSyncResponse {
    pub modpack_id: String,
    pub mod_loader: String,
    pub minecraft_version: String,
    pub mods: String,
    pub name: String,
    pub last_synced: i64,
    pub owners: Vec<OwnerPublic>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OwnerPublic {
    pub username: String,
    pub admin: bool,
}

impl QuadrantSyncResponse {
    pub fn new(value: crate::util::entities::quadrant_sync::Model) -> Self {
        QuadrantSyncResponse {
            modpack_id: value.modpack_id,
            mod_loader: value.mod_loader,
            minecraft_version: value.minecraft_version,
            mods: value.mods,
            name: value.name,
            last_synced: value.last_synced.timestamp(),
            owners: Vec::new(),
        }
    }
}
