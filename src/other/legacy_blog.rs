use actix_web::{
    HttpRequest, HttpResponse, Responder, get,
    http::header::ContentType,
    web::{self, Data},
};
use chrono::prelude::*;
use rss::{Channel<PERSON>uilder, Item, ItemBuilder, SourceBuilder};
use sea_orm::*;
use serde::{Deserialize, Serialize};

use crate::WebData;
use crate::util::entities::blogposts;

/// Represents the structure of a blog post request
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
struct ArticlePostRequest {
    title: String,
    link: String,
    description: String,
    author: String,
    categories: Vec<String>,
    enclosure: String,
    enclosure_mime_type: String,
    content: String,
}

/// Converts a SeaORM blogpost model to an RSS Item
impl From<blogposts::Model> for Item {
    fn from(article: blogposts::Model) -> Self {
        // Create categories if they exist
        let categories = if !article.categories.is_empty() {
            let cats: Vec<rss::Category> = article
                .categories
                .split(',')
                .map(|cat| {
                    rss::CategoryBuilder::default()
                        .name(cat.to_string())
                        .build()
                })
                .collect();
            cats
        } else {
            Vec::new()
        };

        // Create enclosure if enabled
        let enclosure = if article.enclosure_enabled {
            Some(
                rss::EnclosureBuilder::default()
                    .url(article.enclosure)
                    .length(article.enclosure_length.to_string())
                    .mime_type(article.enclosure_mime_type)
                    .build(),
            )
        } else {
            None
        };

        // Build and return the RSS item
        ItemBuilder::default()
            .title(Some(article.title))
            .link(Some(article.link))
            .description(Some(article.description))
            .author(Some(article.author))
            .categories(categories)
            .enclosure(enclosure)
            .pub_date(Some(article.date))
            .source(Some(SourceBuilder::default().url(article.source).build()))
            .content(Some(article.content))
            .guid(Some(rss::Guid {
                value: article.guid,
                permalink: true,
            }))
            .build()
    }
}

/// Builds the RSS channel with all blog posts
async fn build_channel(data: Data<WebData>) -> Result<String, anyhow::Error> {
    // Fetch all blog posts from the database
    let articles: Vec<blogposts::Model> = blogposts::Entity::find()
        .all(&data.conn)
        .await
        .map_err(|e| anyhow::Error::msg(format!("Failed to fetch blog posts: {}", e)))?;

    // Convert blog posts to RSS items
    let rss_items: Vec<Item> = articles.into_iter().map(Item::from).collect();

    // Build the RSS channel
    let channel = ChannelBuilder::default()
        .title("MrQuantumOFF's blog".to_string())
        .link("https://blog.mrquantumoff.dev".to_string())
        .description("An RSS feed for news related to Minecraft Modpack Manager, Linux, gaming and other stuff.".to_string())
        .managing_editor(Some("MrQuantumOFF (Demir Yerli) <<EMAIL>>".to_string()))
        .copyright(Some("Copyright (c) 2023, MrQuantumOFF (Demir Yerli), Bultek.".to_string()))
        .items(rss_items)
        .language(Some("en-US".to_string()))
        .last_build_date(Some(Utc::now().to_rfc2822()))
        .build();

    Ok(channel.to_string())
}

/// Handler for the /blog_old.rss endpoint
/// Returns an RSS feed of all blog posts
#[get("/blog_old.rss")]
async fn get_rss(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    match build_channel(data).await {
        Ok(channel) => HttpResponse::Ok()
            .content_type(ContentType::xml())
            .insert_header(("Access-Control-Allow-Methods", "GET"))
            .insert_header(("Access-Control-Allow-Headers", "*"))
            .insert_header(("Access-Control-Allow-Origin", "*"))
            .insert_header(("Access-Control-Max-Age", "86400"))
            .body(channel),
        Err(_) => HttpResponse::InternalServerError().body("Failed to get RSS"),
    }
}
