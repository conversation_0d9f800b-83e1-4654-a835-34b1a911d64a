use actix_web::{HttpRequest, HttpResponse, Responder, get, post, web};
use log::error;
use qntapi::WebData;
use qstring::QString;
use reqwest::Method;
use sea_orm::*;
use serde::{Deserialize, Serialize};

use crate::util::entities::uabot_verified_users;

/// Discord OAuth2 response structure
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
struct DiscordAccessInfo {
    access_token: String,
    expires_in: i32,
    refresh_token: String,
    scope: String,
    token_type: String,
}

/// Discord user information structure
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
struct DiscordUser {
    id: String,
}

/// Handles direct verification using a verification code
#[post("/api/v1/verifydiscorduser")]
async fn verifydiscorduser(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    // Parse the verification code from query string
    let qs = QString::from(_req.query_string());
    let code = match qs.get("code") {
        Some(code) => match code.parse::<i64>() {
            Ok(code) => code,
            Err(_) => {
                error!("Code error");
                return HttpResponse::BadRequest().body("Invalid verification code");
            }
        },
        None => return HttpResponse::BadRequest().body("No verification code specified"),
    };

    // Find and update the user verification status
    let result = verify_user_with_code(&data.conn, code).await;
    match result {
        Ok(true) => HttpResponse::Ok().body("OK"),
        Ok(false) => HttpResponse::BadRequest().body("Invalid code"),
        Err(e) => {
            error!("Verification error: {}", e);
            HttpResponse::InternalServerError().body(e.to_string())
        }
    }
}

/// Handles OAuth2 verification flow for Discord users
#[get("/api/v1/verifydiscorduser/oauth2")]
async fn logindiscorduser(_req: HttpRequest, data: web::Data<WebData>) -> impl Responder {
    // Parse the OAuth2 code from query string
    let qs = QString::from(_req.query_string());
    let code = match qs.get("code") {
        Some(code) => code,
        None => return HttpResponse::BadRequest().body("No verification code specified"),
    };

    // Exchange code for access token
    let access_token = match get_discord_access_token(&data, code).await {
        Ok(token) => token,
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Get Discord user information
    let user_id = match get_discord_user_id(&access_token).await {
        Ok(id) => id,
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    };

    // Verify the user with their Discord ID
    match verify_user_with_discord_id(&data.conn, user_id).await {
        Ok(true) => HttpResponse::TemporaryRedirect()
            .insert_header(("Location", "https://mrquantumoff.dev/success"))
            .body("OK"),
        Ok(false) | Err(_) => HttpResponse::TemporaryRedirect()
            .insert_header(("Location", "https://mrquantumoff.dev/error"))
            .body("ERROR"),
    }
}

/// Verifies a user using their verification code
async fn verify_user_with_code(conn: &DatabaseConnection, code: i64) -> Result<bool, DbErr> {
    let user = uabot_verified_users::Entity::find()
        .filter(uabot_verified_users::Column::VerificationCode.eq(code))
        .one(conn)
        .await?;

    if let Some(user) = user {
        // Update user verification status
        let mut user: uabot_verified_users::ActiveModel = user.into();
        user.verification_code = Set(-1);
        user.verified = Set(true);
        user.update(conn).await?;
        Ok(true)
    } else {
        Ok(false)
    }
}

/// Verifies a user using their Discord ID
async fn verify_user_with_discord_id(
    conn: &DatabaseConnection,
    user_id: i64,
) -> Result<bool, DbErr> {
    let user = uabot_verified_users::Entity::find()
        .filter(uabot_verified_users::Column::UserId.eq(user_id))
        .one(conn)
        .await?;

    if let Some(user) = user {
        // Update user verification status
        let mut user: uabot_verified_users::ActiveModel = user.into();
        user.verification_code = Set(-2);
        user.verified = Set(true);
        user.update(conn).await?;
        Ok(true)
    } else {
        Ok(false)
    }
}

/// Gets Discord access token using OAuth2 code
async fn get_discord_access_token(data: &WebData, code: &str) -> Result<String, String> {
    let client = reqwest::Client::new();
    let params = QString::new(vec![
        (
            "client_id",
            data.discord_verify_oauth2.client_id.to_string(),
        ),
        ("grant_type", "authorization_code".to_string()),
        (
            "redirect_uri",
            data.discord_verify_oauth2.redirect_uri.clone(),
        ),
        (
            "client_secret",
            data.discord_verify_oauth2.client_secret.clone(),
        ),
        ("code", code.to_string()),
    ]);

    let request = client
        .request(Method::POST, "https://discord.com/api/oauth2/token")
        .header("User-Agent", "MrQuantumOFF API (mrquantumoff.dev)")
        .header("Content-Type", "application/x-www-form-urlencoded")
        .header("Accept-Encoding", "application/x-www-form-urlencoded")
        .body(params.to_string())
        .build()
        .map_err(|e| e.to_string())?;

    let response = client
        .execute(request)
        .await
        .map_err(|e| e.to_string())?
        .text()
        .await
        .map_err(|e| e.to_string())?;

    error!("Access token result: {}", response);

    let access_info: DiscordAccessInfo =
        serde_json::from_str(&response).map_err(|_| response.to_string())?;

    Ok(access_info.access_token)
}

/// Gets Discord user ID using access token
async fn get_discord_user_id(access_token: &str) -> Result<i64, String> {
    let client = reqwest::Client::new();
    let request = client
        .request(Method::GET, "https://discord.com/api/users/@me")
        .header("User-Agent", "MrQuantumOFF API (mrquantumoff.dev)")
        .header("Content-Type", "application/x-www-form-urlencoded")
        .header("Accept-Encoding", "application/x-www-form-urlencoded")
        .bearer_auth(access_token)
        .build()
        .map_err(|e| e.to_string())?;

    let response = client
        .execute(request)
        .await
        .map_err(|e| e.to_string())?
        .text()
        .await
        .map_err(|e| e.to_string())?;

    error!("User id result: {}", response);

    let user: DiscordUser = serde_json::from_str(&response).map_err(|_| response.to_string())?;

    user.id.parse::<i64>().map_err(|e| e.to_string())
}
