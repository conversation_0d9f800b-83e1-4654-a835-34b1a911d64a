use std::collections::HashMap;

use actix_web::{HttpRequest, HttpResponse, Responder, put, web};
use chrono::prelude::*;
use qntapi::{ApiKeySearchFilter, WebData, get_api_keys};
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::util::entities::tauri_updates::{self};

#[derive(Clone, Debug, Serialize, Deserialize)]
struct LatestRelease {
    #[serde(rename = "pub_date")]
    pub pub_date: DateTime<Utc>,
    pub notes: String,
    pub version: String,
    pub platforms: HashMap<String, PlatformInfo>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
struct PlatformInfo {
    pub signature: String,
    pub url: String,
}

/// Endpoint for submitting new Tauri application updates
#[put("/api/any/{app}/add_updates/{channel}/{new_version}")]
async fn submit_new_version(
    _req: HttpRequest,
    path: web::Path<(String, String, String)>,
    webdata: web::Data<WebData>,
) -> impl Responder {
    let (app, channel, new_version) = path.into_inner();

    // Verify API key
    let api_key_filter = ApiKeySearchFilter {
        product: "updater".to_string(),
        scope: "update".to_string(),
    };
    let get_keys_res = get_api_keys(webdata.as_ref().to_owned(), api_key_filter).await;
    if get_keys_res.is_err() {
        return HttpResponse::InternalServerError()
            .body("Failed to verify your API key (doesn't mean that it's invalid)");
    }
    let authentication_keys: Vec<String> = get_keys_res.unwrap();

    // Validate authorization header
    if _req.headers().get("Authorization").is_none()
        || !authentication_keys.contains(
            &_req
                .headers()
                .get("Authorization")
                .unwrap()
                .to_str()
                .unwrap()
                .to_owned(),
        )
    {
        return HttpResponse::Forbidden().body("Your API key is invalid.");
    }

    // Construct GitHub release URL
    let base_url = format!(
        "https://github.com/mrquantumoff/quadrant/releases/download/{}/latest.json",
        new_version
    );

    log::info!("Updating Quadrant version: {base_url}");

    // Fetch release information
    let latest_release = match reqwest::get(&base_url).await {
        Ok(response) => response,
        Err(e) => {
            return HttpResponse::InternalServerError().body(format!(
                "Failed to get the latest release info ({})",
                e.to_string()
            ));
        }
    };

    // Parse release information
    let latest_release: LatestRelease = match latest_release.json().await {
        Ok(release) => release,
        Err(_) => {
            return HttpResponse::InternalServerError().body("Failed to decode new version info");
        }
    };

    // Create update records
    let mut updates: Vec<tauri_updates::ActiveModel> = Vec::new();

    // Process each platform
    for (k, v) in latest_release.platforms {
        let platform_arch: Vec<&str> = k.split('-').collect();
        let platform = platform_arch[0];
        let arch = platform_arch[1];

        let update = tauri_updates::ActiveModel {
            product: Set(app.clone()),
            arch: Set(arch.to_string()),
            branch: Set(channel.clone()),
            signature: Set(v.signature),
            version: Set(latest_release.version.clone()),
            url: Set(v.url),
            pub_date: Set(latest_release.pub_date.into()),
            public: Set(false),
            version_id: Set(Uuid::now_v7().to_string()),
            platform: Set(platform.to_string()),
        };
        updates.push(update);
    }

    // Delete existing version if present
    match tauri_updates::Entity::delete_many()
        .filter(tauri_updates::Column::Version.eq(latest_release.version.clone()))
        .exec(&webdata.conn)
        .await
    {
        Ok(_) => (),
        Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
    }

    // Insert new updates
    for update in updates {
        match update.insert(&webdata.conn).await {
            Ok(_) => (),
            Err(e) => return HttpResponse::InternalServerError().body(e.to_string()),
        }
    }

    HttpResponse::Accepted().body("Updated/Created successfully")
}
