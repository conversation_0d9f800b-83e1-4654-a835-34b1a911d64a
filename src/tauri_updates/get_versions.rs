use actix_web::{HttpRequest, HttpResponse, Responder, get, web};
use sea_orm::{ColumnTrait, En<PERSON>tyTrait, Query<PERSON>ilter, QueryOrder};

use crate::util::entities::tauri_updates;
use qntapi::WebData;

/// Endpoint for retrieving Tauri application updates
/// Path format: /api/any/{app}/updates/{channel}/{target}/{architecture}/{current_version}
#[get("/api/any/{app}/updates/{channel}/{target}/{architecture}/{current_version}")]
async fn get_versions(
    _req: HttpRequest,
    path: web::Path<(String, String, String, String)>,
    data: web::Data<WebData>,
) -> impl Responder {
    let (app, channel, target, architecture) = path.into_inner();

    // Query the database for matching updates
    let find_res = tauri_updates::Entity::find()
        .filter(tauri_updates::Column::Product.eq(app))
        .filter(tauri_updates::Column::Arch.eq(architecture))
        .filter(tauri_updates::Column::Platform.eq(target))
        .order_by_desc(tauri_updates::Column::PubDate)
        .filter(tauri_updates::Column::Public.eq(true))
        .all(&data.conn)
        .await;

    match find_res {
        Ok(versions) => {
            // If the latest version matches the branch, return it
            if let Some(version) = versions.first() {
                if version.branch == channel {
                    return match serde_json::to_string_pretty(&version) {
                        Ok(json) => HttpResponse::Ok().body(json),
                        Err(_) => HttpResponse::InternalServerError()
                            .body("Failed to serialize version info"),
                    };
                }
            }

            // Get the newest stable version otherwise (first one due to ordering)
            let versions = versions
                .into_iter()
                .filter(|v| v.branch == "stable")
                .collect::<Vec<_>>();
            if let Some(version) = versions.first() {
                match serde_json::to_string_pretty(&version) {
                    Ok(json) => HttpResponse::Ok().body(json),
                    Err(_) => {
                        HttpResponse::InternalServerError().body("Failed to serialize version info")
                    }
                }
            } else {
                HttpResponse::NotFound().body("No supported versions")
            }
        }
        Err(_) => HttpResponse::InternalServerError().body("Failed to get version info"),
    }
}
