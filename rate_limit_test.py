import requests
import threading

threads = []


def send_request():
    url = "https://localhost:443"  # replace with your desired URL
    headers = {
        "User-Agent": "Rate Limit test (mrquantumoff.dev)"
    }  # replace with your desired headers

    while True:
        try:
            response = requests.get(url, headers=headers)
            print(response.status_code)
        except:
            pass


for i in range(10000):
    t = threading.Thread(target=send_request)
    t.start()
